import { NgModule } from '@angular/core';
import { SharedModule } from '../@shared/shared.module';
import { AuthRoutingModule } from './auth-routing.module';
import { AuthComponent } from './auth.component';
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';
import { LoginComponent } from './login/login.component';
import { ResetPasswordComponent } from './reset-password/reset-password.component';

const COMPONENTS = [LoginComponent, AuthComponent];

@NgModule({
  declarations: [...COMPONENTS, LoginComponent, ForgotPasswordComponent, ResetPasswordComponent],
  imports: [SharedModule, AuthRoutingModule]
})
export class AuthModule {}
