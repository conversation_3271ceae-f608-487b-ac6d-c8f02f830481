<div [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="col-sm-12 col-xs-12 col-md-12 col-lg-12">
    <qesolar-company-logo></qesolar-company-logo>
  </div>
  <div class="col-sm-12 col-xs-12 col-md-12 col-lg-12">
    <h1 id="title" class="title">Forgot Password</h1>
    <p class="sub-title">Enter your email address and we’ll send a link to reset your password</p>

    <form (ngSubmit)="requestPass()" #requestPassForm="ngForm" aria-labelledby="title">
      <div class="form-control-group">
        <label class="label" for="input-email">Enter address:</label>
        <input
          nbInput
          fullWidth
          [(ngModel)]="forgotEmail.email"
          #email="ngModel"
          name="email"
          id="input-email"
          placeholder="Email address"
          autofocus
          [status]="email.dirty ? (email.invalid ? 'danger' : 'primary') : ''"
          required
          pattern=".+@.+\..+"
          [attr.aria-invalid]="email.invalid && email.touched ? true : null"
        />
        <ng-container *ngIf="email.invalid && email.touched">
          <p class="caption status-danger" *ngIf="email.errors?.required">Email is required!</p>
          <p class="caption status-danger" *ngIf="email.errors?.pattern">Email format is incorrect</p>
        </ng-container>
      </div>

      <button
        nbButton
        fullWidth
        status="primary"
        size="large"
        [disabled]="isSubmitted || !requestPassForm.valid"
        [class.btn-pulse]="isSubmitted"
      >
        Request password
      </button>
    </form>

    <section class="sign-in-or-up" aria-label="Sign in or sign up">
      <p><a class="text-link" routerLink="../login">Back to Log In</a></p>
    </section>
  </div>
</div>
