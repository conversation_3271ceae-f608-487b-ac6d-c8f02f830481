export class UserAuthenticationReq {
  public email: string = '';
  public password: string = '';
  public rememberMe: boolean;
  public isFromCustomerGatewayAPI: boolean;
  public mfaCode: string = null;
  public questionID: number = null;
  public question: string = '';
  public questionAnswer: string = '';
  public hideInputValues: boolean = true;
}

export class UserAuthenticationRes {
  public isAuthenticated: boolean;
  public id_Token: string;
  public refresh_Token: string;
  public isMFAUserLoggedIn: boolean;
  public isMFARequired: boolean;
  public isWrongMFACode: boolean;
  public isMFACodeExpire: boolean;
  public errorMessage: string;
  public isShowSecurityQuestion: boolean;
  public isAccountLocked: boolean;
  public isForcedToChangePassword: boolean;

  constructor(resp: Partial<UserAuthenticationRes>) {
    this.isAuthenticated = resp?.isAuthenticated ?? false;
    this.id_Token = resp?.id_Token ?? '';
    this.refresh_Token = resp?.refresh_Token ?? '';
    this.isMFAUserLoggedIn = resp?.isMFAUserLoggedIn ?? false;
    this.isMFARequired = resp?.isMFARequired ?? false;
    this.isWrongMFACode = resp?.isWrongMFACode ?? false;
    this.isMFACodeExpire = resp?.isMFACodeExpire ?? false;
    this.errorMessage = resp?.errorMessage ?? '';
    this.isShowSecurityQuestion = resp?.isShowSecurityQuestion ?? false;
    this.isAccountLocked = resp?.isAccountLocked ?? false;
    this.isForcedToChangePassword = resp?.isForcedToChangePassword ?? false;
  }
}

export class LoginTokenResponse {
  public id_Token: string;
  public refresh_Token: string;
  public isMFAUserLoggedIn: boolean;

  constructor(id_Token: string, refresh_Token: string, isMFAUserLoggedIn: boolean) {
    this.id_Token = id_Token ?? '';
    this.refresh_Token = refresh_Token ?? '';
    this.isMFAUserLoggedIn = isMFAUserLoggedIn ?? false;
  }
}
