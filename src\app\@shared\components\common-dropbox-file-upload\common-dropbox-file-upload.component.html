<div class="modal-content" id="attachmentFiles" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header modal-fixed-header">
    <div class="d-flex align-items-center w-100">
      <h6 *ngIf="!isFileEditMode">Add File Details</h6>
      <h6 *ngIf="isFileEditMode">Edit File Details</h6>
      <div class="ms-auto">
        <button type="button" class="close" aria-label="Close" (click)="_bsModalRef.hide()">
          <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
        </button>
      </div>
    </div>
  </div>
  <form
    name="uploadFilesForm"
    #uploadFilesForm="ngForm"
    aria-labelledby="title"
    autocomplete="off"
    (ngSubmit)="uploadFilesForm?.form?.valid && saveUploadFilesDetails()"
  >
    <div class="modal-body fileUpload">
      <div class="row">
        <div class="col-md-12">
          <label class="label">{{ isFileEditMode ? 'File Name' : 'File Attachment' }}</label>
          <div *ngIf="!isFileEditMode" class="dropZone" ngFileDragDrop (fileDropped)="getUpload($event)">
            <input type="file" #filesInput multiple (change)="getUpload($event.target.files)" />
            <em class="fa fa-cloud-upload-alt text-primary iconimageupload" aria-hidden="true"></em><br />
            <h5 class="fw-bold">Drag & Drop files to attachment</h5>
            <label style="text-transform: none" class="fw-bold">or Browse to choose a file </label>
          </div>
          <ul *ngIf="files.length">
            <li *ngFor="let item of files; let i = index" class="mb-2">
              <span>{{ item.name }}</span>
              <em
                (click)="deleteFile(i)"
                nbTooltip="Delete"
                nbTooltipPlacement="top"
                nbTooltipStatus="danger"
                aria-hidden="true"
                class="fa fa-times-circle text-danger ms-2"
              ></em>
            </li>
          </ul>
          <ul *ngIf="isFileEditMode && !files.length">
            <li class="mb-2">
              <span> {{ fileItemObj.fileName }}</span>
            </li>
          </ul>
        </div>
        <div class="col-12 mt-2">
          <label class="label" for="fileTagsApply">File Tags </label>
          <ng-select
            name="fileTagsApply"
            id="region-drop-down"
            class="sfl-track-dropdown"
            [multiple]="true"
            [items]="filesTagList"
            bindLabel="name"
            bindValue="id"
            [(ngModel)]="fileTagIds"
            #fileTagsApply="ngModel"
            notFoundText="No File Tag Found"
            placeholder="Select File Tag"
            [closeOnSelect]="false"
            (search)="onFilter($event)"
            (ngModelChange)="reorderTags()"
            (close)="filteredAppliedTags = []"
          >
            <ng-template ng-header-tmp *ngIf="filesTagList && filesTagList.length">
              <button type="button" (click)="toggleSelectUnselectAllTags(true)" class="btn btn-sm btn-primary me-2">Select all</button>
              <button type="button" (click)="toggleSelectUnselectAllTags(false)" class="btn btn-sm btn-primary ml-2">Unselect all</button>
            </ng-template>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" name="item-{{ index }}" />
              {{ item.name }}
            </ng-template>
            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
              <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                <span class="ng-value-label">{{ item.name }}</span>
                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
              </div>
              <div class="ng-value" *ngIf="items.length > 2">
                <span class="ng-value-label">+{{ items.length - 2 }} </span>
              </div>
            </ng-template>
          </ng-select>
        </div>
        <div class="col-md-12">
          <label class="label">Notes</label>
          <textarea
            nbInput
            fullWidth
            name="fileDetailsNote"
            id="input-fileDetailsNote"
            #fileDetailsNote="ngModel"
            [(ngModel)]="notes"
            maxlength="5120"
          >
          </textarea>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button nbButton type="button" status="basic" size="medium" (click)="_bsModalRef.hide()">Cancel</button>
      <button nbButton status="primary" size="medium" type="submit" id="closeSubmit">Save</button>
    </div>
  </form>
</div>
