import { Component, Input, OnInit } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Subject } from 'rxjs';

@Component({
  selector: 'sfl-show-message',
  templateUrl: './show-message.component.html',
  styleUrls: ['./show-message.component.scss']
})
export class ShowMessageComponent implements OnInit {
  @Input() message: string;
  @Input() title: string;
  public onClose: Subject<boolean>;

  constructor(public _bsModalRef: BsModalRef) {}

  public ngOnInit(): void {
    this.onClose = new Subject();
  }

  public onCancel(): void {
    this.onClose.next(true);
    this._bsModalRef.hide();
  }
}
