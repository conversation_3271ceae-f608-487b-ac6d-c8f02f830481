<div class="row" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="col-sm-12 col-xs-12 col-md-12 col-lg-12">
    <qesolar-company-logo></qesolar-company-logo>
  </div>
  <ng-container *ngIf="!emailPassScreenHidden">
    <div class="col-sm-12 col-xs-12 col-md-12 col-lg-12">
      <h3 id="title" class="title mt-2 mb-2">Login</h3>
      <p class="sub-title">Hello! Log in with your email.</p>

      <form
        id="loginForm"
        name="loginForm"
        (ngSubmit)="loginForm.valid && login()"
        #loginForm="ngForm"
        aria-labelledby="title"
        autocomplete="off"
      >
        <div class="form-control-group">
          <label class="label" for="input-email">Email address:</label>
          <input
            nbInput
            fullWidth
            class="status-basic"
            [(ngModel)]="userAuthenticationReqObj.email"
            #email="ngModel"
            name="email"
            id="input-email"
            placeholder="Email address"
            autofocus
            [status]="email.dirty ? (email.invalid ? 'danger' : 'success') : 'basic'"
            required
            pattern=".+@.+\..+"
            [attr.aria-invalid]="email.invalid && email.touched ? true : null"
          />
          <ng-container *ngIf="email.invalid && email.touched">
            <p class="caption status-danger" *ngIf="email.errors?.required">Email is required</p>
            <p class="caption status-danger" *ngIf="email.errors?.pattern">Email format is incorrect</p>
          </ng-container>
        </div>

        <div class="form-control-group">
          <label class="label" for="input-password">Password:</label>
          <input
            nbInput
            fullWidth
            class="status-basic"
            [(ngModel)]="userAuthenticationReqObj.password"
            #password="ngModel"
            id="password"
            name="password"
            type="password"
            placeholder="Password"
            autocomplete="on"
            [status]="password.dirty ? (password.invalid ? 'danger' : 'success') : 'basic'"
            required
            [attr.aria-invalid]="password.invalid && password.touched ? true : null"
          />
          <ng-container *ngIf="password.invalid && password.touched">
            <p class="caption status-danger" *ngIf="password.errors?.required">Password is required</p>
          </ng-container>
          <span class="label-with-link mt-1">
            <a class="forgot-password caption-2 text-link" [routerLink]="ROUTES.FORGOTPASSWORD">Forgot Password?</a>
          </span>
        </div>
        <button nbButton fullWidth status="primary" size="large" [class.btn-pulse]="submitted">Log In</button>
      </form>
    </div>
  </ng-container>
  <ng-container *ngIf="emailPassScreenHidden">
    <ng-container *ngIf="userAuthenticationResObj.isShowSecurityQuestion || userAuthenticationResObj.isMFARequired">
      <form
        id="identityVerificationForm"
        name="identityVerificationForm"
        (ngSubmit)="identityVerificationForm.valid && login()"
        #identityVerificationForm="ngForm"
        aria-labelledby="title"
        autocomplete="off"
      >
        <div class="col-sm-12 col-xs-12 col-md-12 col-lg-12">
          <h3 id="title" class="title mt-2 mb-2">Verify your identity</h3>
          <p class="sub-title">Please complete the identity verification process to continue and access your account.</p>

          <ng-container *ngIf="userAuthenticationResObj.isShowSecurityQuestion">
            <div class="form-control-group">
              <div class="row align-items-center justify-content-center mb-3">
                <div class="col">
                  <ng-select
                    name="question"
                    [items]="securityQuestionList"
                    bindLabel="question"
                    bindValue="questionID"
                    #question="ngModel"
                    [(ngModel)]="userAuthenticationReqObj.questionID"
                    notFoundText="No Question Found"
                    placeholder="Select Question"
                    appendTo=""
                    [required]="userAuthenticationResObj.isShowSecurityQuestion"
                    [clearable]="false"
                    autocomplete="''"
                    (ngModelChange)="userAuthenticationReqObj.questionAnswer = ''"
                  >
                  </ng-select>
                  <ng-container *ngIf="question.invalid && (question.dirty || question.touched)">
                    <p class="caption status-danger" *ngIf="question.errors?.required">Question is required</p>
                  </ng-container>
                </div>
              </div>
              <ng-container *ngIf="userAuthenticationReqObj.questionID">
                <div class="row align-items-center justify-content-center mb-3">
                  <div class="col">
                    <div class="d-flex align-items-center">
                      <input
                        nbInput
                        fullWidth
                        [(ngModel)]="userAuthenticationReqObj.questionAnswer"
                        #questionAnswer="ngModel"
                        name="questionAnswer"
                        id="questionAnswer{{ index + 1 }}"
                        class="form-control w-100"
                        [type]="userAuthenticationReqObj.hideInputValues ? 'password' : 'text'"
                        class="form-control"
                        placeholder="Enter Answer"
                        [status]="questionAnswer.dirty ? (questionAnswer.invalid ? 'danger' : 'success') : 'basic'"
                        [disabled]="!userAuthenticationReqObj.questionID"
                        [required]="userAuthenticationReqObj.questionID && userAuthenticationResObj.isShowSecurityQuestion"
                        autocomplete="new-password"
                        [attr.aria-invalid]="questionAnswer.invalid && questionAnswer.touched ? true : null"
                      />
                      <div class="ms-2">
                        <button
                          nbButton
                          fullWidth
                          status="basic"
                          outline
                          status="primary"
                          size="medium"
                          type="button"
                          [disabled]="!userAuthenticationReqObj.questionID"
                          (click)="userAuthenticationReqObj.hideInputValues = !userAuthenticationReqObj.hideInputValues"
                        >
                          <nb-icon [icon]="userAuthenticationReqObj.hideInputValues ? 'eye-outline' : 'eye-off-2-outline'"></nb-icon>
                        </button>
                      </div>
                    </div>
                    <ng-container *ngIf="questionAnswer.invalid && (questionAnswer.dirty || questionAnswer.touched)">
                      <p class="caption status-danger" *ngIf="questionAnswer.errors?.required">Answer is required</p>
                    </ng-container>
                  </div>
                </div>
              </ng-container>
            </div>
          </ng-container>

          <ng-container *ngIf="userAuthenticationResObj.isMFARequired || userAuthenticationResObj.isShowSecurityQuestion">
            <div class="form-control-group">
              <div class="row align-items-center justify-content-center mb-3">
                <div class="col">
                  <input
                    type="hidden"
                    id="mfaCodeInput"
                    name="mfaCodeInput"
                    [(ngModel)]="userAuthenticationReqObj.mfaCode"
                    #mfaCodeInput="ngModel"
                    [required]="userAuthenticationResObj.isMFARequired || userAuthenticationResObj.isShowSecurityQuestion"
                    minlength="6"
                    maxlength="6"
                  />
                  <sfl-otp-input
                    #sflOtpInput
                    [options]="sflOtpInputOptions"
                    (otpComplete)="userAuthenticationReqObj.mfaCode = $event"
                    (otpChange)="userAuthenticationReqObj.mfaCode = $event.join('')"
                  ></sfl-otp-input>
                </div>
              </div>
            </div>
          </ng-container>
        </div>

        <button
          nbButton
          fullWidth
          status="primary"
          size="large"
          [class.btn-pulse]="submitted"
          [disabled]="identityVerificationForm.invalid"
        >
          Verify {{ userAuthenticationResObj.isShowSecurityQuestion ? '' : userAuthenticationResObj.isMFARequired ? ' Code' : '' }}
        </button>
      </form>
      <div class="d-flex justify-content-end mt-3">
        <span class="label-with-link mt-1">
          <a class="text-link" (click)="reloadWindow()">Back to Log In</a>
        </span>
      </div>
    </ng-container>
    <ng-container
      *ngIf="
        !(
          userAuthenticationResObj.isShowSecurityQuestion ||
          userAuthenticationResObj.isMFARequired ||
          userAuthenticationResObj.isAccountLocked
        ) && userAuthenticationResObj.isWrongMFACode
      "
    >
      <div class="col-sm-12 col-xs-12 col-md-12 col-lg-12">
        <h3 id="title" class="title mt-2 mb-2">Code Entry Failed</h3>
        <p class="sub-title">The code you entered is invalid. Kindly verify the code and re-enter it.</p>
      </div>
    </ng-container>
    <ng-container
      *ngIf="
        !(
          userAuthenticationResObj.isShowSecurityQuestion ||
          userAuthenticationResObj.isMFARequired ||
          userAuthenticationResObj.isAccountLocked
        ) && userAuthenticationResObj.isMFACodeExpire
      "
    >
      <div class="col-sm-12 col-xs-12 col-md-12 col-lg-12">
        <h3 id="title" class="title mt-2 mb-2">Verification Code Expired</h3>
        <p class="sub-title">The verification code you entered has expired. Please restart your login process.</p>
      </div>
    </ng-container>
    <ng-container
      *ngIf="
        !(userAuthenticationResObj.isShowSecurityQuestion || userAuthenticationResObj.isMFARequired) &&
        userAuthenticationResObj.isAccountLocked
      "
    >
      <div class="col-sm-12 col-xs-12 col-md-12 col-lg-12">
        <h3 id="title" class="title mt-2 mb-2">Account Temporarily Locked</h3>
        <p class="sub-title">
          {{
            userAuthenticationResObj.errorMessage
              ? userAuthenticationResObj.errorMessage
              : 'Your account has been locked due to multiple failed identity verification attempts.'
          }}
        </p>
      </div>
    </ng-container>
  </ng-container>
</div>

<button hidden type="button" #codeEntryFailedButton (click)="openModal(codeEntryFailedTemplate)"></button>
<button hidden type="button" #codeExpiredButton (click)="openModal(codeExpiredTemplate)"></button>
<button hidden type="button" #accountLockedButton (click)="openModal(accountLockedTemplate)"></button>

<ng-template #codeEntryFailedTemplate>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Code Entry Failed</h4>
  </div>
  <div class="modal-body">The code you entered is invalid. Kindly verify the code and re-enter it.</div>
  <div class="modal-footer">
    <button
      type="button"
      class="btn btn-primary"
      #closeModalButton
      (click)="modalRef.hide(); userAuthenticationResObj.isMFARequired ? reset() : reloadWindow()"
    >
      OK
    </button>
  </div>
</ng-template>

<ng-template #codeExpiredTemplate>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Verification Code Expired</h4>
  </div>
  <div class="modal-body">The verification code you entered has expired. Please restart your login process.</div>
  <div class="modal-footer">
    <button type="button" class="btn btn-primary" #closeModalButton (click)="modalRef.hide(); reloadWindow()">OK</button>
  </div>
</ng-template>

<ng-template #accountLockedTemplate>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Account Temporarily Locked</h4>
  </div>
  <div class="modal-body">
    {{
      userAuthenticationResObj.errorMessage
        ? userAuthenticationResObj.errorMessage
        : 'Your account has been locked due to multiple failed identity verification attempts.'
    }}
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-primary" #closeModalButton (click)="modalRef.hide(); reloadWindow()">OK</button>
  </div>
</ng-template>
