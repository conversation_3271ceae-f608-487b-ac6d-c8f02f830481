import { AfterContentInit, ContentChild, Directive, ElementRef, Input } from '@angular/core';

@Directive({
  selector: '[sflAutoFocus]'
})
export class AutoFocusDirective implements AfterContentInit {
  @ContentChild('otpInputElement', { static: false })
  firstInput!: ElementRef<HTMLInputElement>;

  @Input() sflAutoFocus!: boolean;

  ngAfterContentInit(): void {
    if (this.sflAutoFocus && this.firstInput) {
      this.firstInput.nativeElement.focus();
    }
  }
}
