import { Component, Input, OnInit, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subject, Subscription } from 'rxjs';
import { ReportService } from '../../../entities/report/report.service';
import { AlertService } from '../../services';
import {
  BulkNCTicketCreateResponse,
  NonConformanceDto,
  TicketPriorityMapping,
  TicketTypeMapping
} from './bulk-create-non-cpnformance.model';

@Component({
  selector: 'sfl-bulk-create-non-conformance-tickets',
  templateUrl: './bulk-create-non-conformance-tickets.component.html',
  styleUrls: ['./bulk-create-non-conformance-tickets.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class BulkCreateNonConformanceTicketsComponent implements OnInit {
  public onClose: Subject<any>;
  @Input() nonConformances: NonConformanceDto[];
  @Input() workOrderId: number;
  @Input() reportId: string;
  @Input() assessmentId: number;
  @Input() assessmentType: string;
  @Input() frequencyType: string;
  subscription: Subscription = new Subscription();
  @ViewChild('cancelConfirmModelTemplate', { static: false }) cancelConfirmModelTemplate: TemplateRef<any>;
  isCreated = false;
  loading = false;
  priorityList = TicketPriorityMapping;
  ticketTypeList = TicketTypeMapping;
  selectedBulkNC = [];
  isMasterSel = false;
  isNCItemUpdated = false;
  modalRef: BsModalRef;
  isSelectAllAllowed = false;
  constructor(
    public _bsModalRef: BsModalRef,
    private readonly alertService: AlertService,
    private readonly modalService: BsModalService,
    public options: ModalOptions,
    private readonly router: Router,
    private readonly reportService: ReportService
  ) {}

  ngOnInit(): void {
    this.onClose = new Subject();
    this.isSelectAllAllowed = this.nonConformances.every(n => n.ticketNumber);
  }

  public onCancel(template: TemplateRef<any>): void {
    this._bsModalRef.setClass('d-none');
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-md'
    };
    setTimeout(() => {
      this.modalRef = this.modalService.show(template, ngModalOptions);
    }, 0);
  }

  closeModel() {
    this.modalRef.hide();
    // navigate to Work oder
    this.router.navigate(['/entities/workorders/add'], {
      queryParams: {
        id: this.assessmentId,
        assementType: this.assessmentType,
        frequencyType: this.frequencyType
      }
    });
  }

  selectDeselectAll() {
    const selectedIds = new Set(this.selectedBulkNC.map(items => items.ncGuid));

    this.nonConformances.forEach(nc => {
      if (!nc.ticketNumber) {
        nc.isSelected = this.isMasterSel;
      }

      if (this.isMasterSel) {
        if (!selectedIds.has(nc.ncGuid)) {
          this.selectedBulkNC.push(nc);
        }
      } else {
        this.selectedBulkNC = this.selectedBulkNC.filter(item => item.ncGuid !== nc.ncGuid && !item.ticketNumber);
      }
    });
  }

  singleNCCheckChanged(ncItem) {
    if (ncItem.isSelected) {
      if (!this.selectedBulkNC.some(items => items.ncGuid === ncItem.ncGuid)) {
        this.selectedBulkNC.push(ncItem);
      }
    } else {
      this.selectedBulkNC = this.selectedBulkNC.filter(items => items.ncGuid !== ncItem.ncGuid);
    }

    this.isMasterSel = this.nonConformances.every(ncItem => ncItem.isSelected);
  }

  onCreateBulkTickets() {
    this.loading = true;
    const params = {
      workOrderId: this.workOrderId,
      reportId: this.reportId,
      ncData: this.selectedBulkNC
        .filter(item => !item.ticketNumber)
        .map(item => ({
          ncGuid: item.ncGuid,
          order: item.order,
          component: item.component || 0,
          componentStr: item.componentStr,
          issue: item.issue,
          location: item.location,
          actions: item.actions,
          priority: item.priority,
          ticketType: item.ticketType,
          isNCItemUpdated: this.isNCItemUpdated
        }))
    };
    this.subscription.add(
      this.reportService.createNCBulkTicket(params).subscribe({
        next: (res: BulkNCTicketCreateResponse) => {
          if (res && res.status === 1) {
            this.nonConformances.forEach(element => {
              const match = res.resultTicket.find(a1 => a1.ncGuid === element.ncGuid);
              if (match) {
                element.ticketNumber = match.ticketNumber || '';
              }
            });
            this.alertService.showSuccessToast(res.message);
            this.isCreated = true;
          } else {
            this.alertService.showErrorToast(res.message);
          }
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }
}
