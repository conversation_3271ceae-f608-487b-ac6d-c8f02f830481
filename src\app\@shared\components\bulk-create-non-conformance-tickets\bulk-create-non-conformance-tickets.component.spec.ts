import { ComponentFixture, TestBed } from '@angular/core/testing';

import { BulkCreateNonConformanceTicketsComponent } from './bulk-create-non-conformance-tickets.component';

describe('BulkCreateNonConformanceTicketsComponent', () => {
  let component: BulkCreateNonConformanceTicketsComponent;
  let fixture: ComponentFixture<BulkCreateNonConformanceTicketsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ BulkCreateNonConformanceTicketsComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(BulkCreateNonConformanceTicketsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
