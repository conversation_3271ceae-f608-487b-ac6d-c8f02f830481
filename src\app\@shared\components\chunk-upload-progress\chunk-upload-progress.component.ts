import { Component, OnInit } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { debounceTime } from 'rxjs';
import { ChunkUploadProgressDetails } from '../../models/share';
import { CommonService } from '../../services/common.service';

@Component({
  selector: 'sfl-chunk-upload-progress',
  templateUrl: './chunk-upload-progress.component.html',
  styleUrls: ['./chunk-upload-progress.component.scss']
})
export class ChunkUploadProgressComponent implements OnInit {
  chunkUploadProgressDetails: ChunkUploadProgressDetails[];
  showProgress = false;
  constructor(private readonly commonService: CommonService, public _bsModalRef: BsModalRef) {}

  ngOnInit(): void {
    this.commonService.isChunkUploadInProgress$.subscribe({
      next: res => {
        this.showProgress = res;
      }
    });

    this.commonService.chunkUploadDetails$.pipe(debounceTime(500)).subscribe({
      next: (progressDetails: ChunkUploadProgressDetails[]) => {
        this.chunkUploadProgressDetails = progressDetails;
      }
    });
  }

  calculateProgress(currentChunk: number, totalChunks: number): number {
    let progress = totalChunks ? Math.floor((currentChunk / totalChunks) * 100) : 50;
    if (progress === 100) return 99;
    return progress;
  }
}
