import { Directive, ElementRef, HostListener } from '@angular/core';
import { NgModel } from '@angular/forms';

@Directive({
  selector: '[sflNumbersOnly]'
})
export class NumbersOnlyDirective {
  private readonly el: HTMLInputElement;

  constructor(private readonly elementRef: ElementRef, private readonly ngModel: NgModel) {
    this.el = this.elementRef.nativeElement;
  }

  @HostListener('keydown', ['$event']) onkeydown(event) {
    const value = (<HTMLInputElement>event.target).value;
    const DEFAULT_DECIMAL_VALUE = 0.0;

    if (
      [46, 8, 9, 27, 13].indexOf(event.keyCode) !== -1 ||
      // Allow: Ctrl+A
      (event.keyCode === 65 && (event.ctrlKey || event.metaKey)) ||
      // Allow: Ctrl+C
      (event.keyCode === 67 && (event.ctrlKey || event.metaKey)) ||
      // Allow: Ctrl+V
      (event.keyCode === 86 && (event.ctrlKey || event.metaKey)) ||
      // Allow: Ctrl+X
      (event.keyCode === 88 && (event.ctrlKey || event.metaKey)) ||
      // Allow: home, end, left, right
      (event.keyCode >= 35 && event.keyCode <= 39)
    ) {
      // let it happen, don't do anything
      return;
    }

    // Ensure that it is a number and contains only one decimal point
    if ((event.shiftKey || event.keyCode < 48 || event.keyCode > 57) && (event.keyCode < 96 || event.keyCode > 105)) {
      if (event.keyCode === 110 || event.keyCode === 190) {
        if (value.toString().includes('.')) {
          event.preventDefault();
        }
        if (!value.length && event.key === '.') {
          this.ngModel.update.emit(DEFAULT_DECIMAL_VALUE);
        }
      } else {
        event.preventDefault();
      }
    }
  }
}
