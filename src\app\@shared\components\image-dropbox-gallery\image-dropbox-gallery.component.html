<div class="appSpinner_gallery" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="d-flex align-items-center modal-header drop-box-header">
    <div>
      <h6>Image Gallery ({{ totalCount }})</h6>
      <span>{{ requestParamsConfig.entityNumber }}{{ activityDate ? ' - (' + (activityDate | date : dateFormat) + ')' : '' }}</span>
    </div>
    <div class="ms-auto d-flex align-items-center">
      <button
        *ngIf="totalCount > 0"
        nbButton
        status="primary"
        size="small"
        id="downloadFolderBtn"
        class="float-end me-2"
        [disabled]="!isAnyImageSelected()"
        (click)="downloadAsFolder()"
      >
        <span class="d-none d-lg-inline-block">
          <em class="fa fa-download"></em>
          Download Folder</span
        >
        <em class="d-inline-block d-lg-none fa fa fa-download"></em>
      </button>
      <label for="fileUpload" class="file-upload-btn float-end me-2" *ngIf="userRole !== 'customer' && !isTruckRollGallery">
        <span class="d-none d-lg-inline-block">
          <em class="fa fa-upload"></em>
          Upload
        </span>
        <em class="d-inline-block d-lg-none fa fa fa-upload"></em>
        <input
          id="fileUpload"
          #fileInput
          type="file"
          accept="image/*"
          multiple
          style="display: none"
          (change)="uploadImagesToGallery($event)"
        />
      </label>
      <button type="button" class="close" aria-label="Close" (click)="onCancel()">
        <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
      </button>
    </div>
  </div>
  <div id="page-top" class="drop-box-body">
    <div class="row">
      <div class="col-12 col-xl-7 px-3 mt-3 right-border">
        <div *ngIf="dropBoxImageList?.length; else noDataFound" class="position-relative">
          <p-galleria
            [(value)]="dropBoxImageList"
            [containerStyle]="{ 'min-width': '90%' }"
            [numVisible]="1"
            [showThumbnails]="false"
            [(activeIndex)]="activeIndex"
            [showItemNavigators]="false"
            (activeIndexChange)="getActiveIndexChanges($event)"
          >
            <ng-template pTemplate="item" let-item>
              <div class="d-flex align-items-center justify-content-center">
                <em
                  class="pi pi-angle-left me-3"
                  [ngClass]="{ 'text-muted': activeIndex === 0 }"
                  style="font-size: 2rem"
                  (click)="prev()"
                ></em>
                <div class="d-flex flex-column">
                  <div class="d-flex align-items-center justify-content-between date-edit-btn-info mb-2">
                    <p class="mb-0">
                      <span class="fw-bold">Date Taken: </span> {{ item?.dateTaken ? (item?.dateTaken | date : dateFormat) : '' }}
                    </p>
                    <button
                      *ngIf="userRole !== 'customer'"
                      nbButton
                      status="primary"
                      size="small"
                      id="edit-tags"
                      class="float-end me-2"
                      nbTooltip="Edit tags"
                      nbTooltipPlacement="top"
                      nbTooltipStatus="primary"
                      (click)="item.isPreviewImageEditTag = true"
                    >
                      <em class="pi pi-pencil"></em>
                    </button>
                  </div>
                  <div class="image-preview">
                    <img [src]="item.fileUrl" onError="src='assets/images/no-image-found.jpg'" />
                    <div class="image-top-info" *ngIf="item?.isCustomerFacing && userRole !== 'customer'">
                      <div class="mb-0 info-badge ms-2">
                        <p class="mb-0 fw-bold">Customer Facing</p>
                      </div>
                    </div>
                    <div class="expand-icon">
                      <div class="expand-btn">
                        <button
                          nbButton
                          status="primary"
                          size="small"
                          id="expandbtn"
                          class="float-end m-1"
                          nbTooltip="Expand"
                          nbTooltipPlacement="top"
                          nbTooltipStatus="primary"
                          (click)="expandImage(item.fileUrl)"
                        >
                          <em aria-hidden="true" class="fa fa-expand-alt text-light cursor-pointer"></em>
                        </button>
                      </div>
                    </div>
                    <div class="name-preview-btn">
                      <span class="image-name fw-bold"> {{ item.fileName }} </span>
                      <div class="preview-btn">
                        <button
                          nbButton
                          status="primary"
                          size="small"
                          id="downloadbtn"
                          class="float-end m-1"
                          nbTooltip="Download"
                          nbTooltipPlacement="top"
                          nbTooltipStatus="primary"
                          (click)="downloadPreviewFile(item.id, item.fileName)"
                        >
                          <em class="pi pi-download"></em>
                        </button>
                        <button
                          nbButton
                          status="primary"
                          size="small"
                          id="copybtn"
                          class="float-end m-1"
                          nbTooltip="Copy link"
                          nbTooltipPlacement="top"
                          nbTooltipStatus="primary"
                          (click)="copyImageLink(item.fileUrl)"
                        >
                          <em class="pi pi-copy"></em>
                        </button>
                      </div>
                    </div>
                  </div>
                  <div class="mt-3">
                    <div class="row w-100">
                      <div class="col-md-6">
                        <div class="row">
                          <p class="mb-2 fw-bold">Condition Tags</p>

                          <div class="d-flex align-items-center flex-wrap" *ngIf="!item.isPreviewImageEditTag">
                            <ng-container *ngIf="item.conditionalTagTxt?.length">
                              <span class="tag-info-badge me-2 mt-2 fw-bold" *ngFor="let tagName of item.conditionalTagTxt">{{
                                tagName
                              }}</span>
                            </ng-container>
                            <ng-container *ngIf="!item.conditionalTagTxt?.length">N/A</ng-container>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="row">
                          <p class="mb-2 fw-bold">Device Tags</p>
                        </div>
                        <div class="d-flex align-items-center flex-wrap" *ngIf="!item.isPreviewImageEditTag">
                          <ng-container *ngIf="item.deviceTagTxt?.length">
                            <span class="tag-info-badge me-2 mt-2 fw-bold" *ngFor="let tagName of item.deviceTagTxt">{{ tagName }}</span>
                          </ng-container>
                          <ng-container *ngIf="!item.deviceTagTxt?.length">N/A</ng-container>
                        </div>
                      </div>
                    </div>
                    <div class="row" *ngIf="item.isPreviewImageEditTag">
                      <div class="col-md-6 mt-2">
                        <ng-select
                          id="conditional-Tag-single-drop-down"
                          class="sfl-track-dropdown"
                          name="conditionalTags"
                          [multiple]="true"
                          bindValue="id"
                          bindLabel="name"
                          [items]="conditionalTagsList"
                          [(ngModel)]="item.conditionalTag"
                          [closeOnSelect]="false"
                          notFoundText="No Condition Tags Found"
                          placeholder="Add Condition Tags"
                          (search)="onFilter($event, 'singleImageConditionalTag')"
                          (close)="filteredAppliedTags.singleImageConditionalTag = []"
                        >
                          <ng-template ng-header-tmp *ngIf="conditionalTagsList && conditionalTagsList.length">
                            <button
                              type="button"
                              (click)="toggleSingleImageSelectUnselectAllTags(item, 'singleImageConditionalTag', conditionalTagsList, true)"
                              class="btn btn-sm btn-primary me-2"
                            >
                              Select all
                            </button>
                            <button
                              type="button"
                              (click)="
                                toggleSingleImageSelectUnselectAllTags(item, 'singleImageConditionalTag', conditionalTagsList, false)
                              "
                              class="btn btn-sm btn-primary ml-2"
                            >
                              Unselect all
                            </button>
                          </ng-template>
                          <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" name="item-{{ index }}" />
                            {{ item.name }}
                          </ng-template>
                          <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                            <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                              <span class="ng-value-label">{{ item.name }}</span>
                              <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                            </div>
                            <div class="ng-value" *ngIf="items.length > 2">
                              <span class="ng-value-label">+{{ items.length - 2 }} </span>
                            </div>
                          </ng-template>
                        </ng-select>
                      </div>
                      <div class="col-md-6 mt-2">
                        <ng-select
                          id="device-Tag-single-drop-down"
                          class="sfl-track-dropdown"
                          name="deviceTags"
                          [multiple]="true"
                          bindValue="id"
                          bindLabel="name"
                          [items]="deviceTagsList"
                          [(ngModel)]="item.deviceTag"
                          [closeOnSelect]="false"
                          notFoundText="No Device Tags Found"
                          placeholder="Add Device Tags"
                          (search)="onFilter($event, 'singleImageDeviceTag')"
                          (close)="filteredAppliedTags.singleImageDeviceTag = []"
                        >
                          <ng-template ng-header-tmp *ngIf="deviceTagsList && deviceTagsList.length">
                            <button
                              type="button"
                              (click)="toggleSingleImageSelectUnselectAllTags(item, 'singleImageDeviceTag', deviceTagsList, true)"
                              class="btn btn-sm btn-primary me-2"
                            >
                              Select all
                            </button>
                            <button
                              type="button"
                              (click)="toggleSingleImageSelectUnselectAllTags(item, 'singleImageDeviceTag', deviceTagsList, false)"
                              class="btn btn-sm btn-primary ml-2"
                            >
                              Unselect all
                            </button>
                          </ng-template>
                          <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" name="item-{{ index }}" />
                            {{ item.name }}
                          </ng-template>
                          <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                            <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                              <span class="ng-value-label">{{ item.name }}</span>
                              <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                            </div>
                            <div class="ng-value" *ngIf="items.length > 2">
                              <span class="ng-value-label">+{{ items.length - 2 }} </span>
                            </div>
                          </ng-template>
                        </ng-select>
                      </div>
                      <div class="col-12 mt-2">
                        <div class="d-flex align-items-center justify-content-end">
                          <button
                            nbButton
                            status="secondary"
                            size="small"
                            id="tagRemovebtn"
                            class="mt-1 me-2"
                            (click)="addRemovePreviewImagesTags(item.id, item.conditionalTag, item.deviceTag, false)"
                          >
                            Remove Tags
                          </button>
                          <button
                            nbButton
                            status="primary"
                            size="small"
                            id="tagApplybtn"
                            class="mt-1"
                            (click)="addRemovePreviewImagesTags(item.id, item.conditionalTag, item.deviceTag, true)"
                          >
                            Apply Tags
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <em
                  class="pi pi-angle-right ms-3"
                  [ngClass]="{ 'text-muted': activeIndex === dropBoxImageList.length - 1 }"
                  style="font-size: 2rem"
                  (click)="next()"
                ></em>
              </div>
            </ng-template>
          </p-galleria>
        </div>
        <ng-template #noDataFound><p *ngIf="!loading" class="text-center mt-5">No images found</p></ng-template>
      </div>
      <div class="col-12 col-xl-5 px-3 mt-3">
        <div class="images-list-main">
          <div class="filter-border">
            <div class="dropbox-filters">
              <div class="row align-items-center">
                <div class="col-12 col-md-6">
                  <div class="mb-3">
                    <label class="label" for="conditionalTags">Condition Tag</label>
                    <ng-select
                      id="conditional-Tags-drop-down"
                      class="sfl-track-dropdown"
                      name="conditionalTags"
                      [multiple]="true"
                      bindValue="id"
                      bindLabel="name"
                      [items]="conditionalTagsList"
                      [(ngModel)]="galleryFilterModel.sortedConditionTags"
                      [closeOnSelect]="false"
                      notFoundText="No Condition Tags Found"
                      placeholder="Select Condition Tags"
                      (search)="onFilter($event, 'sortedConditionTags')"
                      (close)="filteredAppliedTags.sortedConditionTags = []"
                    >
                      <ng-template ng-header-tmp *ngIf="conditionalTagsList && conditionalTagsList.length">
                        <button
                          type="button"
                          (click)="toggleSelectUnselectSortedTags('sortedConditionTags', conditionalTagsList, true)"
                          class="btn btn-sm btn-primary me-2"
                        >
                          Select all
                        </button>
                        <button
                          type="button"
                          (click)="toggleSelectUnselectSortedTags('sortedConditionTags', conditionalTagsList, false)"
                          class="btn btn-sm btn-primary ml-2"
                        >
                          Unselect all
                        </button>
                      </ng-template>
                      <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" name="item-{{ index }}" />
                        {{ item.name }}
                      </ng-template>
                      <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                        <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                          <span class="ng-value-label">{{ item.name }}</span>
                          <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                        </div>
                        <div class="ng-value" *ngIf="items.length > 2">
                          <span class="ng-value-label">+{{ items.length - 2 }} </span>
                        </div>
                      </ng-template>
                    </ng-select>
                  </div>
                </div>
                <div class="col-12 col-md-6">
                  <div class="mb-3">
                    <label class="label" for="deviceTags">Device Tag</label>
                    <ng-select
                      id="device-Tags-drop-down"
                      class="sfl-track-dropdown"
                      name="deviceTags"
                      [multiple]="true"
                      bindValue="id"
                      bindLabel="name"
                      [items]="deviceTagsList"
                      [(ngModel)]="galleryFilterModel.sortedDeviceTags"
                      [closeOnSelect]="false"
                      notFoundText="No Device Tags Found"
                      placeholder="Select Device Tags"
                      (search)="onFilter($event, 'sortedDeviceTags')"
                      (close)="filteredAppliedTags.sortedDeviceTags = []"
                    >
                      <ng-template ng-header-tmp *ngIf="deviceTagsList && deviceTagsList.length">
                        <button
                          type="button"
                          (click)="toggleSelectUnselectSortedTags('sortedDeviceTags', deviceTagsList, true)"
                          class="btn btn-sm btn-primary me-2"
                        >
                          Select all
                        </button>
                        <button
                          type="button"
                          (click)="toggleSelectUnselectSortedTags('sortedDeviceTags', deviceTagsList, false)"
                          class="btn btn-sm btn-primary ml-2"
                        >
                          Unselect all
                        </button>
                      </ng-template>
                      <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" name="item-{{ index }}" />
                        {{ item.name }}
                      </ng-template>
                      <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                        <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                          <span class="ng-value-label">{{ item.name }}</span>
                          <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                        </div>
                        <div class="ng-value" *ngIf="items.length > 2">
                          <span class="ng-value-label">+{{ items.length - 2 }} </span>
                        </div>
                      </ng-template>
                    </ng-select>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-12 col-md-6">
                <div class="d-flex ml-auto mb-3" *ngIf="userRole !== 'customer'">
                  <div class="d-flex">
                    <nb-toggle [(checked)]="requestParamsConfig.isCustomerFacing" status="primary" class="me-3"></nb-toggle>
                  </div>
                  <p class="mb-0 me-2" for="customer">Customer Facing</p>
                </div>
              </div>
              <div class="col-12 col-md-6 text-end">
                <div class="mb-3">
                  <button
                    id="dropbox-view-data-btn"
                    class="me-2"
                    nbButton
                    status="primary"
                    size="small"
                    type="button"
                    (click)="onTagSorting(false)"
                  >
                    View Data
                  </button>
                  <button id="dropbox-view-data-btn" nbButton status="primary" size="small" type="button" (click)="onTagSorting(true)">
                    Clear
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="image-listing mt-3">
            <div class="d-flex align-items-center justify-content-between mb-2">
              <nb-checkbox *ngIf="dropBoxImageList?.length" [(checked)]="isAllImagesSelected" (change)="selectAllImages()"
                ><p class="mb-0">Select All</p></nb-checkbox
              >
              <button
                *ngIf="dropBoxImageList?.length && userRole !== 'customer' && userRole !== 'analyst'"
                nbButton
                status="danger"
                size="small"
                id="backbtn"
                class="float-end"
                (click)="deleteSelectedImages()"
              >
                Delete
              </button>
            </div>
            <!-- <div class="row g-0 fix-listing-area" (scroll)="onScroll($event)"> -->
            <div class="row g-0 fix-listing-area">
              <ng-container *ngIf="dropBoxImageList?.length; else uploadImages">
                <div
                  class="col-12 col-md-4 pe-3 pb-3"
                  *ngFor="
                    let img of dropBoxImageList
                      | paginate
                        : {
                            itemsPerPage: requestParamsConfig.itemsCount,
                            currentPage: currentPage,
                            totalItems: totalCount
                          };
                    let imageIndex = index
                  "
                >
                  <div class="solar-image">
                    <img
                      [src]="img.thumbnailUrl ? img.thumbnailUrl : img.fileUrl"
                      [ngClass]="{ previewed: imageIndex === activeIndex }"
                      (click)="getItAsCurrentPreview(imageIndex)"
                      appImageLoaderDir
                      onError="src='assets/images/no-image-found.jpg'"
                    />
                    <div class="single-image-checkbox">
                      <nb-checkbox
                        [(checked)]="img.isSelectedForPreview"
                        (change)="singleImageCheckChange($event.target.checked)"
                      ></nb-checkbox>
                    </div>
                    <div class="single-image-date">
                      <span class="image-date">{{ img?.dateTaken ? (img?.dateTaken | date : dateFormat) : '' }}</span>
                    </div>
                  </div>
                </div>
              </ng-container>
              <ng-template #uploadImages>
                <div class="col-12" *ngIf="!loading && userRole !== 'customer' && !isTruckRollGallery">
                  <p class="text-center mt-5">Upload images to gallery using upload button</p>
                </div>
              </ng-template>
            </div>
            <div class="mt-2 d-md-flex align-items-center" *ngIf="dropBoxImageList?.length">
              <!-- <div class="d-flex align-items-center">
                <label class="mb-0">Items per page: </label>
                <ng-select
                  class="ms-2"
                  [(ngModel)]="pageSize"
                  [clearable]="false"
                  [searchable]="false"
                  (change)="onChangeSize()"
                  appendTo="body"
                >
                  <ng-option value="5">15</ng-option>
                  <ng-option value="50">50</ng-option>
                  <ng-option value="100">100</ng-option>
                </ng-select>
              </div> -->
              <!-- <strong class="ms-md-3">Total: {{ total }}</strong> -->
              <div class="ms-md-auto ms-sm-0">
                <pagination-controls (pageChange)="onPageChange($event)" class="paginate"></pagination-controls>
              </div>
            </div>
          </div>
          <div class="row p-3" *ngIf="userRole !== 'customer' && dropBoxImageList?.length">
            <div class="col-12">
              <p class="" for="contract-drop-down">Apply Tags To Image</p>
              <div class="mb-3">
                <ng-select
                  id="conditional-Tags-drop-down"
                  class="sfl-track-dropdown"
                  name="conditionalTags"
                  [multiple]="true"
                  bindValue="id"
                  bindLabel="name"
                  [items]="conditionalTagsList"
                  [(ngModel)]="appliedTags.conditionalTags"
                  [closeOnSelect]="false"
                  notFoundText="No Condition Tags Found"
                  placeholder="Add Condition Tags"
                  (search)="onFilter($event, 'conditionalTags')"
                  (close)="filteredAppliedTags.conditionalTags = []"
                >
                  <ng-template ng-header-tmp *ngIf="conditionalTagsList && conditionalTagsList.length">
                    <button
                      type="button"
                      (click)="toggleSelectUnselectAllTags('conditionalTags', conditionalTagsList, true)"
                      class="btn btn-sm btn-primary me-2"
                    >
                      Select all
                    </button>
                    <button
                      type="button"
                      (click)="toggleSelectUnselectAllTags('conditionalTags', conditionalTagsList, false)"
                      class="btn btn-sm btn-primary ml-2"
                    >
                      Unselect all
                    </button>
                  </ng-template>
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" name="item-{{ index }}" />
                    {{ item.name }}
                  </ng-template>
                  <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                    <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                      <span class="ng-value-label">{{ item.name }}</span>
                      <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                    </div>
                    <div class="ng-value" *ngIf="items.length > 2">
                      <span class="ng-value-label">+{{ items.length - 2 }} </span>
                    </div>
                  </ng-template>
                </ng-select>
              </div>
              <div class="mb-3">
                <ng-select
                  id="device-Tags-drop-down"
                  class="sfl-track-dropdown"
                  name="deviceTags"
                  [multiple]="true"
                  bindValue="id"
                  bindLabel="name"
                  [items]="deviceTagsList"
                  [(ngModel)]="appliedTags.deviceTags"
                  [closeOnSelect]="false"
                  notFoundText="No Device Tags Found"
                  placeholder="Add Device Tags"
                  (search)="onFilter($event, 'deviceTags')"
                  (close)="filteredAppliedTags.deviceTags = []"
                >
                  <ng-template ng-header-tmp *ngIf="deviceTagsList && deviceTagsList.length">
                    <button
                      type="button"
                      (click)="toggleSelectUnselectAllTags('deviceTags', deviceTagsList, true)"
                      class="btn btn-sm btn-primary me-2"
                    >
                      Select all
                    </button>
                    <button
                      type="button"
                      (click)="toggleSelectUnselectAllTags('deviceTags', deviceTagsList, false)"
                      class="btn btn-sm btn-primary ml-2"
                    >
                      Unselect all
                    </button>
                  </ng-template>
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" name="item-{{ index }}" />
                    {{ item.name }}
                  </ng-template>
                  <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                    <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                      <span class="ng-value-label">{{ item.name }}</span>
                      <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                    </div>
                    <div class="ng-value" *ngIf="items.length > 2">
                      <span class="ng-value-label">+{{ items.length - 2 }} </span>
                    </div>
                  </ng-template>
                </ng-select>
              </div>
              <div class="col-12 mt-2">
                <div class="d-flex align-items-center justify-content-end">
                  <button
                    nbButton
                    status="secondary"
                    size="small"
                    id="tagRemovebtn"
                    class="mt-1 me-2"
                    (click)="addRemoveMultipleImagesTags(false)"
                  >
                    Remove Tags
                  </button>
                  <button nbButton status="primary" size="small" id="tagApplybtn" class="mt-1" (click)="addRemoveMultipleImagesTags(true)">
                    Apply Tags
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
