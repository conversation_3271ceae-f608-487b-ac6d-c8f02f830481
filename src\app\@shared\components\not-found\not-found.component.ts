import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { NbMenuService } from '@nebular/theme';

@Component({
  selector: 'qesolar-not-found',
  styleUrls: ['./not-found.component.scss'],
  templateUrl: './not-found.component.html'
})
export class NotFoundComponent {
  constructor(private readonly menuService: NbMenuService, private readonly router: Router) {}

  goToHome() {
    // if require, need to update the routing url based on the logged in user's role and permissions as needed
    this.router.navigate(['/entities/dashboard']);
    // this.menuService.navigateHome();
  }
}
