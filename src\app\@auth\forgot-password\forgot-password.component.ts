import { ChangeDetectorRef, Component } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { APP_ROUTES } from '../../@shared/constants';
import { AlertService } from '../../@shared/services';
import { AuthService } from '../auth.service';

@Component({
  selector: 'qesolar-forgot-password',
  templateUrl: './forgot-password.component.html',
  styles: []
})
export class ForgotPasswordComponent {
  subscription: Subscription = new Subscription();
  readonly ROUTES = APP_ROUTES;
  isSubmitted = false;
  forgotEmail: any = {};
  loading = false;

  constructor(
    private readonly router: Router,
    private readonly authService: AuthService,
    private readonly alertService: AlertService,
    private readonly cdf: ChangeDetectorRef
  ) {}

  requestPass() {
    return new Promise((resolve, reject) => {
      this.loading = true;
      this.isSubmitted = true;
      this.subscription.add(
        this.authService.forgotPassword(this.forgotEmail).subscribe({
          next: (res: any) => {
            if (res) {
              if (res.isSent !== false) {
                this.alertService.showSuccessToast(res.message);
                this.loading = false;
                this.router.navigate(['login']);
              } else {
                this.loading = true;
                this.isSubmitted = true;
                this.alertService.showErrorToast(res.message);
                this.isSubmitted = false;
                this.loading = false;
              }
            }
          },
          error: e => {
            this.isSubmitted = false;
            this.loading = false;
          }
        })
      );
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
