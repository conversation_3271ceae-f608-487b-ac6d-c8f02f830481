import { AfterContentInit, ContentChildren, Directive, ElementRef, Input, OnChanges, QueryList, SimpleChanges } from '@angular/core';

@Directive({
  selector: '[sflAutoBlur]'
})
export class AutoBlurDirective implements OnChanges, AfterContentInit {
  private inputHTMLElements: HTMLInputElement[] = [];

  @ContentChildren('otpInputElement', { descendants: true })
  inputs!: QueryList<ElementRef<HTMLInputElement>>;

  @Input()
  sflAutoBlur!: boolean;

  @Input()
  isFormValid!: boolean;

  ngOnChanges(changes: SimpleChanges) {
    if (this.sflAutoBlur && this.inputHTMLElements.length > 0 && changes['isFormValid'].currentValue) {
      this.inputHTMLElements.forEach(input => {
        input.blur();
      });
    }
  }

  ngAfterContentInit() {
    this.inputs.forEach(input => {
      this.inputHTMLElements.push(input.nativeElement);
    });
  }
}
