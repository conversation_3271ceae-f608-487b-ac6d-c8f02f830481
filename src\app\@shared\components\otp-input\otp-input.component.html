<form
  sflOtpPaste
  sflInputNavigations
  [regexp]="sflOtpOptions.regexp!"
  [isFormValid]="sflOtpInputArray.valid"
  [sflAutoFocus]="sflOtpOptions.autoFocus!"
  [sflAutoBlur]="sflOtpOptions.autoBlur!"
  [sflOtpAriaLabels]="sflOtpOptions.ariaLabels!"
  [ngClass]="{
    'sfl-blinking-cursor': sflOtpOptions.showBlinkingCursor
  }"
  (valueChange)="handleInputChanges($event)"
  (handlePaste)="handlePasteChange($event)"
  class="sfl-otp-input-form"
  data-testid="sfl-otp-input-form"
  id="sfl-otp-input-form"
>
  <div class="d-flex align-items-center justify-content-center gap-3">
    <input
      *ngFor="let control of sflOtpInputArray.controls; let i = index"
      nbInput
      fullWidth
      #otpInputElement
      [value]="control.value"
      [type]="inputType"
      [inputMode]="sflOtpOptions.inputMode"
      [disabled]="disabled"
      [ngClass]="{
        'sfl-otp-input-disabled': disabled,
        'sfl-otp-input-filled': isInputFilled(i),
        'sfl-otp-input-success': isOTPSuccess,
        'sfl-otp-input-failed': isOTPFailed
      }"
      class="sfl-otp-input-box status-basic"
      maxlength="1"
      spellcheck="false"
      autocomplete="new-password"
      autocapitalize="off"
      autocorrect="off"
      data-testid="sfl-otp-input-box"
      [placeholder]="
        sflOtpOptions.ariaLabels && sflOtpOptions.ariaLabels.length ? (sflOtpOptions.ariaLabels[i] ? sflOtpOptions.ariaLabels[i] : '-') : ''
      "
    />
    <div class="h-100 w-100 d-flex align-items-center justify-content-center" *ngIf="sflOtpOptions.showInputValuesBtn && hideInputValues">
      <button
        nbButton
        fullWidth
        status="basic"
        outline
        status="primary"
        size="medium"
        [disabled]="!sflOtpOptions.showInputValuesBtn && !hideInputValues"
        *ngIf="sflOtpOptions.showInputValuesBtn && hideInputValues"
        (click)="sflOtpOptions.hideInputValues = !sflOtpOptions.hideInputValues"
      >
        <nb-icon [icon]="sflOtpOptions.hideInputValues ? 'eye-outline' : 'eye-off-2-outline'"></nb-icon>
      </button>
    </div>
  </div>
</form>
