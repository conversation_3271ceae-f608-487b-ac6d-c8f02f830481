import { DatePipe } from '@angular/common';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { NavigationEnd, Router } from '@angular/router';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import * as uuid from 'uuid';
import { SiteCheckInService } from '../../../entities/safety/site-checkin/site-checkin.service';
import { SiteService } from '../../../entities/site-management/site.service';
import { SiteCheckinDetails, SiteCheckinOut, SiteDetails } from '../../models/site-checkin-out.model';
import { User } from '../../models/user.model';
import { AlertService } from '../../services';
import { StorageService } from '../../services/storage.service';

@Component({
  selector: 'sfl-site-checkin-out',
  templateUrl: './site-checkin-out.component.html',
  styleUrls: ['./site-checkin-out.component.scss']
})
export class SiteCheckinOutComponent implements OnInit {
  @Input() position;
  siteList: SiteDetails[];
  siteCheckinForm: SiteCheckinOut = new SiteCheckinOut();
  siteCheckOutDetails: SiteCheckinOut = new SiteCheckinOut();
  subscription: Subscription = new Subscription();
  loading = false;
  user: User;
  siteDetails: { customerId: number; portfolioId: number; siteId: number } = { customerId: 0, portfolioId: 0, siteId: 0 };
  siteCheckoutChecklist = [];
  checkoutChecklist = [
    'Contact Portfolio Lead with Update',
    'Production Verified on Monitoring',
    'Tickets Created/Updated',
    'Report/Photos/Thermals Uploaded',
    'Site Secured'
  ];
  visitReasons = ['CM', 'PM', 'Site Audit', 'Vegetation', 'Contracted Services', 'Other'];
  checkoutValidationError = false;
  @ViewChild('siteCheckInForm') siteCheckInNgForm: NgForm;
  updateCheckinDetails = false;
  updateCheckinEntry: boolean;
  triggerReasonValidation: boolean;
  isUserOnCheckinScreen = '';
  private routerSubscription: any;
  currentRoute: string;

  constructor(
    public _bsModalRef: BsModalRef,
    private readonly siteService: SiteService,
    private readonly storageService: StorageService,
    private readonly datePipe: DatePipe,
    private readonly siteCheckInService: SiteCheckInService,
    private readonly alertService: AlertService,
    private readonly router: Router
  ) {}

  ngOnInit(): void {
    this.user = this.storageService.get('user');
    this.loading = true;
    this.subscription.add(
      this.siteService.getAllSitesByPortfolioId(true, null, null).subscribe({
        next: res => {
          this.siteList = res;
          this.loading = false;
        },
        error: () => {
          this.loading = false;
        }
      })
    );
    this.routerSubscription = this.router.events.subscribe(() => {
      this.currentRoute = this.router.url;
    });
    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        this.isUserOnCheckinScreen = event.url;
      }
    });
  }

  onSiteUpdate() {
    const selectedSite = this.siteList.filter(site => site.id === this.siteCheckinForm.siteId)[0];
    this.siteDetails.siteId = selectedSite.id;
    this.siteDetails.customerId = selectedSite.customerId;
    this.siteDetails.portfolioId = selectedSite.portfolioId;
    this.siteCheckinForm.siteTimeZoneOffset = selectedSite.siteTimeZoneOffset ?? null;
  }

  saveSiteCheckIn(isDeleteCheckin = false) {
    if (isDeleteCheckin) {
      const requestData = {
        id: this.siteCheckinForm.id,
        userId: this.siteCheckinForm.userId,
        siteId: this.siteCheckinForm.siteId,
        status: 1,
        isUploaded: 1,
        isDeleted: 1,
        siteTimeZoneOffset: this.siteCheckinForm.siteTimeZoneOffset,
        date: this.siteCheckinForm.date,
        dateEpoch: new Date().valueOf(),
        driveTime: this.siteCheckinForm.driveTime,
        latitude: this.siteCheckinForm.latitude,
        longitude: this.siteCheckinForm.longitude,
        reason: this.siteCheckinForm.reasons.toString(),
        notes: this.siteCheckinForm.notes ?? ''
      };
      const requestPayload = [requestData];
      this.siteCheckInService.fieldTechSiteCheckin(requestPayload).subscribe({
        next: () => {
          this.loading = false;
          this.user.checkedInSiteId = null;
          this.user.userCheckedInId = null;
          this.storageService.set('user', this.user, true);
          this.siteCheckInService.userObjectChangedInStorage$.next(true);
          this.alertService.showSuccessToast('Site check-in cancelled.');
          this.onCancel();
          // if the user is holding the site checkin screen open we need to navigate them in order to avoid any conflicts to the checkin.
          const currentUrl = this.router.url;
          if (currentUrl.includes('/site-checkin/detail')) {
            this.router.navigate(['/entities/safety/site-checkin']);
          }
        },
        error: () => {
          this.loading = false;
        }
      });
    } else {
      this.triggerReasonValidation = true;
      this.siteCheckInNgForm.control.markAllAsTouched();
      if (this.siteCheckInNgForm.invalid || this.siteCheckinForm.reasons.length === 0) {
        return;
      }
      this.loading = true;
      this.triggerReasonValidation = false;
      const date = new Date();

      const siteIdControl = this.siteCheckInNgForm.form.get('siteId');
      const driveTimeControl = this.siteCheckInNgForm.form.get('driveTime');

      if (siteIdControl) {
        siteIdControl.enable(); // Disable siteId control
      }

      if (driveTimeControl) {
        driveTimeControl.enable(); // Disable driveTime control
      }

      if (this.siteCheckinForm.reasons.includes('Other') && !this.siteCheckinForm.otherReason) {
        this.loading = false;
        return;
      }
      this.siteCheckinForm.userId = this.user.userId;
      this.siteCheckinForm.driveTime = this.siteCheckinForm.driveTime ? Number(this.siteCheckinForm.driveTime) : 0;
      this.siteCheckinForm.status = 1;
      this.siteCheckinForm.date =
        date.getFullYear() +
        '-' +
        String(date.getMonth() + 1).padStart(2, '0') +
        '-' +
        String(date.getDate()).padStart(2, '0') +
        'T' +
        String(date.getHours()).padStart(2, '0') +
        ':' +
        String(date.getMinutes()).padStart(2, '0') +
        ':' +
        String(date.getSeconds()).padStart(2, '0') +
        '.' +
        String(date.getMilliseconds()).padStart(3, '0');
      this.siteCheckinForm.latitude = this.position.coords.latitude;
      this.siteCheckinForm.longitude = this.position.coords.longitude;

      if (this.siteCheckinForm.id) {
        this.updateCheckinEntry = true;
      } else {
        this.siteCheckinForm.id = uuid.v4();
      }
      if (this.siteCheckinForm.reasons.includes('Other')) {
        this.siteCheckinForm.reasons.push(this.siteCheckinForm.otherReason);
      }
      this.siteCheckinForm.reason = this.siteCheckinForm.reasons.filter(reson => reson !== 'Other').toString();
      this.siteCheckinForm.dateEpoch = new Date().valueOf();
      this.siteCheckinForm.isDeleted = 0;
      this.siteCheckinForm.isUploaded = 0;

      const requestPayload: any[] = [];
      const requestData = {
        id: this.siteCheckinForm.id,
        userId: this.siteCheckinForm.userId,
        siteId: this.siteCheckinForm.siteId,
        status: 1,
        isUploaded: 0,
        isDeleted: 0,
        siteTimeZoneOffset: this.siteCheckinForm.siteTimeZoneOffset,
        date: this.siteCheckinForm.date,
        dateEpoch: this.siteCheckinForm.dateEpoch,
        driveTime: this.siteCheckinForm.driveTime,
        latitude: this.siteCheckinForm.latitude,
        longitude: this.siteCheckinForm.longitude,
        reason: this.siteCheckinForm.reason,
        notes: this.siteCheckinForm.notes
      };
      requestPayload.push(requestData);

      this.subscription.add(
        this.siteCheckInService.fieldTechSiteCheckin(requestPayload).subscribe({
          next: () => {
            this.loading = false;
            this.alertService.showSuccessToast('Site check-in successful.');
            this.onCancel();
            if (this.updateCheckinDetails) {
              // add the site checkin info to the user object in the storage
              this.user.checkedInSiteId = this.siteCheckinForm.siteId;
              this.user.userCheckedInId = this.siteCheckinForm.id;
              this.updateCheckinDetails = false;
              this.updateCheckinEntry = false;
              this.storageService.set('user', this.user, true);
            } else {
              // add the site checkin info to the user object in the storage
              this.user.checkedInSiteId = this.siteDetails.siteId;
              this.user.userCheckedInId = this.siteCheckinForm.id;
              this.updateCheckinDetails = false;
              this.updateCheckinEntry = false;
              this.storageService.set('user', this.user, true);
              this.router.navigate(['/entities/safety/jha/upload'], {
                queryParams: {
                  checkin: 1,
                  cust: this.siteDetails.customerId,
                  port: this.siteDetails.portfolioId,
                  site: this.siteDetails.siteId
                }
              });
              setTimeout(() => {
                this.siteCheckInService.userObjectChangedInStorage$.next(true);
              }, 500);
            }
          },
          error: () => {
            this.loading = false;
          }
        })
      );
    }
  }

  saveSiteCheckOut() {
    if (this.siteCheckoutChecklist.length !== this.checkoutChecklist.length) {
      this.checkoutValidationError = true;
      return;
    }
    this.checkoutValidationError = false;
    this.loading = true;
    const date = new Date();

    this.siteCheckOutDetails.id = uuid.v4();
    this.siteCheckOutDetails.userId = this.user.userId;
    this.siteCheckOutDetails.driveTime = null;
    this.siteCheckOutDetails.status = 2;
    this.siteCheckOutDetails.date =
      date.getFullYear() +
      '-' +
      String(date.getMonth() + 1).padStart(2, '0') +
      '-' +
      String(date.getDate()).padStart(2, '0') +
      'T' +
      String(date.getHours()).padStart(2, '0') +
      ':' +
      String(date.getMinutes()).padStart(2, '0') +
      ':' +
      String(date.getSeconds()).padStart(2, '0') +
      '.' +
      String(date.getMilliseconds()).padStart(3, '0');
    this.siteCheckOutDetails.latitude = this.position.coords.latitude;
    this.siteCheckOutDetails.longitude = this.position.coords.longitude;
    this.siteCheckOutDetails.reason = this.siteCheckoutChecklist.toString();
    this.siteCheckOutDetails.dateEpoch = new Date().valueOf();
    this.siteCheckOutDetails.isDeleted = 0;
    this.siteCheckOutDetails.isUploaded = 0;
    this.siteCheckOutDetails.notes = null;
    const selectedSite = this.siteList.filter(site => site.id === this.user.checkedInSiteId)[0];
    this.siteCheckOutDetails.siteTimeZoneOffset = selectedSite.siteTimeZoneOffset ?? null;
    this.siteCheckOutDetails.siteId = selectedSite.id;
    const requestPayload: any[] = [];
    requestPayload.push(this.siteCheckOutDetails);

    this.subscription.add(
      this.siteCheckInService.fieldTechSiteCheckin(requestPayload).subscribe({
        next: res => {
          this.loading = false;
          this.user.checkedInSiteId = null;
          this.user.userCheckedInId = null;
          this.storageService.set('user', this.user, true);
          this.siteCheckInService.userObjectChangedInStorage$.next(true);
          this.onCancel();
          this.alertService.showSuccessToast('Site check-out successful.');
          const currentUrl = this.router.url;
          // if the user is holding the site checkin screen open we need to navigate them in order to avoid any conflicts to the checkin.
          if (currentUrl.includes('/site-checkin/detail')) {
            this.router.navigate(['/entities/safety/site-checkin']);
          }
        },
        error: () => {
          this.loading = false;
        }
      })
    );
  }

  onCheckboxChange(event: any, value: string) {
    if (event.target.checked) {
      this.siteCheckoutChecklist.push(value);
    } else {
      this.siteCheckoutChecklist = this.siteCheckoutChecklist.filter(item => item !== value);
    }
    if (this.siteCheckoutChecklist.length === this.checkoutChecklist.length) {
      this.checkoutValidationError = false;
    }
  }

  onVisitReasonChange(event: any, reason: string) {
    const isChecked = event.target.checked;
    if (isChecked) {
      this.siteCheckinForm.reasons.push(reason);
    } else {
      this.siteCheckinForm.reasons = this.siteCheckinForm.reasons.filter(r => r !== reason);
    }
    this.siteCheckinForm.reasons.length ? (this.triggerReasonValidation = false) : (this.triggerReasonValidation = true);
  }

  updateCheckin() {
    this.updateCheckinDetails = true;
    // get the checkin details:
    this.subscription.add(
      this.siteCheckInService.getAudit(this.user.userCheckedInId).subscribe({
        next: (res: SiteCheckinDetails) => {
          const currentCheckinDetails = res.siteCheckInCheckOutAuditDtos.filter(checkin => checkin.id === this.user.userCheckedInId)[0];

          this.siteCheckinForm.id = currentCheckinDetails.id;
          this.siteCheckinForm.userId = currentCheckinDetails.userId;
          this.siteCheckinForm.siteId = currentCheckinDetails.siteId;
          this.siteCheckinForm.userName = currentCheckinDetails.userName;
          this.siteCheckinForm.customer = currentCheckinDetails.customer;
          this.siteCheckinForm.portfolio = currentCheckinDetails.portfolio;
          this.siteCheckinForm.siteName = currentCheckinDetails.siteName;
          this.siteCheckinForm.date = currentCheckinDetails.date;
          this.siteCheckinForm.utcTimeStamp = currentCheckinDetails.utcTimeStamp;
          this.siteCheckinForm.notes = currentCheckinDetails.notes;
          this.siteCheckinForm.driveTime = currentCheckinDetails.driveTime;
          this.siteCheckinForm.siteTimeZoneOffset = currentCheckinDetails.siteTimeZoneOffset;
          this.siteCheckinForm.reasons = currentCheckinDetails.reasonArrays.filter(reason => reason.checked).map(x => x.name);
          this.siteCheckinForm.reason = this.siteCheckinForm.reasons.toString();
          this.siteCheckinForm.latitude = currentCheckinDetails.latitude;
          this.siteCheckinForm.longitude = currentCheckinDetails.longitude;

          if (this.siteCheckinForm.reasons.includes('Other')) {
            this.siteCheckinForm.otherReason = currentCheckinDetails.reasonArrays.filter(reason => reason.name === 'Other')[0].val;
          }
          // this.siteCheckinForm.reasons.push('Other');
          console.log({ siteCheckinForm: this.siteCheckInNgForm });
          const siteIdControl = this.siteCheckInNgForm.form.get('siteId');
          const driveTimeControl = this.siteCheckInNgForm.form.get('driveTime');
          if (siteIdControl) {
            siteIdControl.disable();
          }
          if (driveTimeControl) {
            driveTimeControl.disable();
          }
        },
        error: () => {}
      })
    );
  }

  onCancel(): void {
    this._bsModalRef.hide();
  }
}
