import { Component, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { filter } from 'rxjs';
@Component({
  selector: 'qesolar-footer',
  styleUrls: ['./footer.component.scss'],
  template: `
    <div class="pull-left">
      Powered by
      <strong> <a href="https://www.thesunflowerlab.com/" target="_blank">Sunflower Lab</a></strong>
    </div>
    <ng-container *ngIf="showFooterLogo">
      <img class="qe-placeholder-logo" src="./assets/images/QEST_Placeholder_logo.png" />
    </ng-container>
    <div class="pull-right">
      Copyrights received by
      <strong> <a href="https://www.qesolar.com/" target="_blank">QE Solar </a></strong>
      © {{ currentyear }}
    </div>
  `
})
export class FooterComponent implements OnInit {
  currentyear;
  showFooterLogo = false;
  isRefreshed = true;
  constructor(private readonly router: Router) {
    // need to include footer QEST logo if the page is under Admin section
    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(() => {
      this.isRefreshed = false;
      if (this.router.url.includes('/admin')) {
        this.showFooterLogo = true;
      } else {
        this.showFooterLogo = false;
      }
    });
  }

  ngOnInit(): void {
    this.currentyear = new Date().getFullYear();
    if (this.isRefreshed) {
      this.updateFooterLogo();
    }
  }

  updateFooterLogo() {
    if (this.router.url.includes('/admin')) {
      this.showFooterLogo = true;
    } else {
      this.showFooterLogo = false;
    }
  }
}
