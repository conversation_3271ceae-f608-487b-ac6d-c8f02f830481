import { Component, EventEmitter, Input, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { catchError, concatMap, finalize, Observable, of, Subject, Subscription, takeUntil, tap } from 'rxjs';
import { ChunkUploadProgressDetails } from '../../models/share';
import { AlertService } from '../../services/alert.service';
import { CommonService } from '../../services/common.service';
import { DropboxImageGalleryService } from '../../services/dropbox-image-gallery.service';
import { StorageService } from '../../services/storage.service';
import { TagListResponseModel } from '../image-dropbox-gallery/drop-box.model';
@Component({
  selector: 'sfl-common-dropbox-file-upload',
  templateUrl: './common-dropbox-file-upload.component.html',
  styleUrls: ['./common-dropbox-file-upload.component.scss'],
  encapsulation: ViewEncapsulation?.None
})
export class CommonDropboxFileUploadComponent implements OnInit {
  public onClose: Subject<boolean>;
  @Input() isFileEditMode;
  @Input() fileItemObj;
  @Input() entityDetails;
  @Input() isParentCreateMode;
  @Input() parentModuleName;
  @ViewChild('filesInput') filesInput;
  public isParentRefresh: EventEmitter<any> = new EventEmitter();
  public fileUploadList: EventEmitter<any> = new EventEmitter();
  filesTagList: TagListResponseModel[] = [];
  filteredAppliedTags = [];
  loading = false;
  subscription: Subscription = new Subscription();
  modalRef: BsModalRef;
  userRole;
  files: File[] = [];
  ticketCreateFileUploadList = [];
  fileTagIds = [];
  notes = '';
  private destroy$ = new Subject<void>();
  chunkUploadInProgress = false;
  isUploadInProgress = false;
  constructor(
    public _bsModalRef: BsModalRef,
    private readonly alertService: AlertService,
    private readonly storageService: StorageService,
    private readonly dropBoxService: DropboxImageGalleryService,
    private readonly commonService: CommonService
  ) {}

  ngOnInit(): void {
    if (this.isFileEditMode) {
      this.files = [];
      this.fileTagIds = this.fileItemObj.fileTag;
      this.notes = this.fileItemObj.notes;
    }
    this.userRole = this.storageService.get('user').authorities[0];
    this.getFilesTagList();
  }

  getFilesTagList() {
    this.loading = true;
    this.dropBoxService.getFileTagList().subscribe({
      next: data => {
        if (data) {
          this.filesTagList = data;
        }
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  getUpload(files: any) {
    if (files.length === 0) {
      this.filesInput.nativeElement.value = '';
      return;
    }

    for (let i = 0; i < files.length; i++) {
      const file: File = files[i];
      this.files.push(file);
    }
    if (this.files.length === 0) {
      return;
    }
  }

  saveUploadFilesDetails() {
    this.loading = true;

    if (this.isFileEditMode) {
      this.updateDocumentDetails();
    } else {
      if (this.isParentCreateMode) {
        this.emitTicketCreateFileUploadList();
      } else {
        this.uploadFiles();
      }
    }
  }

  private updateDocumentDetails() {
    const param = {
      fileId: this.fileItemObj.id,
      fileTagIds: this.fileTagIds,
      notes: this.notes
    };

    this.subscription.add(
      this.dropBoxService.updateDocumentsFilesTags(param).subscribe({
        next: res => {
          this.isParentRefresh.emit(true);
          this.alertService.showSuccessToast('File Details updated successfully.');
          this.files = [];
          this.fileTagIds = [];
          this.notes = '';
          this.loading = false;
          this._bsModalRef.hide();
        },
        error: e => {
          this.handleError();
        }
      })
    );
  }

  private emitTicketCreateFileUploadList() {
    for (const file of this.files) {
      this.prepareFilesObjForTicketCreate(file);
    }

    this.fileUploadList.emit(this.ticketCreateFileUploadList);
    this.files = [];
    this.fileTagIds = [];
    this.notes = '';
    this.loading = false;
    this._bsModalRef.hide();
  }

  private uploadFiles() {
    this.loading = false;
    if (this.files.length === 0) {
      this.alertService.showSuccessToast('File attachment is required.');
      return;
    }
    // check the number of files which are > 300 mbs and which are < 200 mbs and prepare two arrays for the same
    // now for the files which are less than 200 mb will be proccessed as belows and
    // for the file greater than 200 will require to be uploaded as chunk
    const bufferChunkSize = 200 * (1024 * 1024);

    if (this.files.length) {
      this.chunkUploadInProgress = true;
      this.commonService.isChunkUploadInProgress$.next(true);

      for (const file of this.files) {
        const fileUploadTimeStamp = new Date().getTime();
        const totalChunks = Math.ceil(file.size / bufferChunkSize);
        this.commonService.totalChunksCount += totalChunks;
        this.startChunkUpload(file, bufferChunkSize, fileUploadTimeStamp, totalChunks);
      }

      this.commonService.remainingChunks$.next(this.commonService.totalChunksCount); // Initialize the counter
    }
  }

  startChunkUpload(file: File, chunkSize: number, fileUploadTimeStamp: number, totalChunks: number) {
    let chunkUpload$: Observable<void> = of(undefined);

    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
      const start = chunkIndex * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const chunk = file.slice(start, end);

      chunkUpload$ = chunkUpload$.pipe(
        concatMap(() =>
          this.uploadChunk(chunk, chunkIndex, totalChunks, file, fileUploadTimeStamp).pipe(
            finalize(() => {
              // Decrease the global counter after each chunk
              this.commonService.remainingChunks$.next(this.commonService.remainingChunks$.value - 1);
              this.commonService.totalChunksCount -= 1; // clear the counter
              // Check if it's the last chunk of the file and remove from chunkDetails
              if (chunkIndex + 1 === totalChunks) {
                this.removeFileFromChunkDetails(fileUploadTimeStamp);
              }
            })
          )
        )
      );
    }

    chunkUpload$.subscribe({
      next: () => {
        console.log(`All chunks uploaded successfully for file: ${file.name}`);
      },
      error: err => console.error(`Error during chunk upload for file: ${file.name}`, err)
    });

    // Monitor when uploads are fully complete
    this.commonService.remainingChunks$.subscribe(remaining => {
      if (remaining === 0) {
        console.log('All files and chunks uploaded.');
        this.chunkUploadInProgress = false;
        this.commonService.isChunkUploadInProgress$.next(false);
        this.commonService.setAutoLogoutValue(false);

        setTimeout(() => {
          if (!this.chunkUploadInProgress) {
            this.alertService.showSuccessToast('Files upload completed.');
            this.commonService.commonChunkUploadDetails = [];
            this.commonService.chunkUploadDetails$.next(null);
            this.commonService.commonUploadFinish$.next(true);
          }
        }, 1000);
      } else {
        this.chunkUploadInProgress = true;
        this.commonService.isChunkUploadInProgress$.next(true);
        this.commonService.setAutoLogoutValue(true);
      }
    });
  }

  uploadChunk(chunk: Blob, chunkIndex: number, totalChunks: number, file: File, fileUploadTimeStamp: number): Observable<void> {
    const bufferChunkSize = 200 * (1024 * 1024);
    const chunkDetails: ChunkUploadProgressDetails = {
      fileName: file.name,
      currentChunk: chunkIndex + 1,
      totalChunks: totalChunks,
      fileUploadTimeStamp: fileUploadTimeStamp
    };
    if (this._bsModalRef) this._bsModalRef.hide();
    let filePartName = '';
    if (file.size > bufferChunkSize) {
      filePartName = `${file.name}.part_${chunkIndex + 1}.${totalChunks}`;
    } else {
      filePartName = `${file.name}`;
    }
    let isChunkUpload = true;
    const formData = new FormData();
    if (this.fileTagIds.length > 0) {
      for (const tag of this.fileTagIds) {
        formData.append('fileTagIds', tag);
      }
    } else {
      formData.append('fileTagIds', null);
    }

    formData.append('customerId', `${this.entityDetails.customerId}`);
    formData.append('id', '0');
    formData.append('portfolioId', `${this.entityDetails.portfolioId}`);
    formData.append('siteId', `${this.entityDetails.siteId}`);
    formData.append('entityId', `${this.entityDetails.entityId}`);
    formData.append('entityNumber', `${this.entityDetails.entityNumber}`);
    formData.append('moduleType', `${this.entityDetails.moduleType}`);
    formData.append('fileType', 'document');
    formData.append('notes', `${this.notes}`);

    formData.append('files', chunk, filePartName);
    formData.append('fileName', filePartName);
    if (file.size > bufferChunkSize) {
      formData.append('totalPart', `${totalChunks}`);
      formData.append('currentPart', `${chunkIndex + 1}`);
      formData.append('fileUploadTimeStamp', `${fileUploadTimeStamp}`);
      formData.append('IsLargeFile', `${isChunkUpload}`);
    } else {
      formData.append('IsLargeFile', `false`);
    }
    return this.commonService.uploadChunk(formData, chunkDetails).pipe(
      takeUntil(this.destroy$),
      tap(() => {
        console.log(`Chunk ${chunkIndex + 1}/${totalChunks} uploaded successfully.`);
      }),
      catchError(err => {
        console.error(`Error uploading chunk ${chunkIndex + 1}/${totalChunks}:`, err);
        throw err; // Propagate error for retry or higher-level handling
      })
    );
  }

  removeFileFromChunkDetails(fileUploadTimeStamp: number) {
    // Filter out the completed file based on fileUploadTimeStamp
    const updatedChunkDetails = this.commonService.chunkUploadDetails$.value.filter(
      details => details.fileUploadTimeStamp !== fileUploadTimeStamp
    );
    setTimeout(() => {
      this.commonService.chunkUploadDetails$.next(updatedChunkDetails);
      this.commonService.commonChunkUploadDetails = [...updatedChunkDetails];
    }, 500);
  }

  private handleError() {
    this.alertService.showWarningToast('Fail to upload File.');
    this.loading = false;
  }

  prepareFilesObjForTicketCreate(file) {
    const fileObj = {
      file: file,
      id: this.generateUniqueId(),
      fileName: file.name,
      fileTag: this.fileTagIds ? this.fileTagIds : [],
      fileTagTxt: this.fileTagIds ? this.getNamesByIds() : [],
      notes: this.notes ? this.notes : null,
      fileType: 'document',
      createdBy: this.getUserName(),
      createdDate: file.lastModifiedDate
    };
    this.ticketCreateFileUploadList.push(fileObj);
  }

  generateUniqueId() {
    const prefix = 'QE_' + Date.now();
    const randomSuffix = Math.random().toString(36).substr(2, 9);
    return prefix + '_' + randomSuffix;
  }

  getNamesByIds() {
    return this.fileTagIds.map(id => {
      const item = this.filesTagList.find(item => item.id === id);
      return item ? item.name : null;
    });
  }

  getUserName() {
    const userInfo = this.storageService.get('user');
    return `${userInfo.firstName} ${userInfo.lastName}`;
  }

  isPlPlusUser(userRole: string): boolean {
    return userRole === 'portfolioManager' || userRole === 'admin' || userRole === 'commercialAssetsManager';
  }

  onFilter(event: any) {
    if (event.term) {
      this.filteredAppliedTags = event.items?.map(element => element.id);
    } else {
      this.filteredAppliedTags = [];
    }
  }

  toggleSelectUnselectAllTags(isSelect = false) {
    if (isSelect) {
      if (!this.filteredAppliedTags.length) {
        this.fileTagIds = this.filesTagList.map(site => site.id);
      } else {
        if (!Array.isArray(this.fileTagIds)) {
          this.fileTagIds = [];
        }
        this.fileTagIds = [...new Set([...this.fileTagIds, ...JSON.parse(JSON.stringify(this.filteredAppliedTags))])];
      }
    } else {
      if (this.filteredAppliedTags.length) {
        this.fileTagIds = this.fileTagIds.filter(x => !this.filteredAppliedTags.includes(x));
      } else {
        this.fileTagIds = [];
      }
    }
  }

  deleteFile(index: number) {
    this.files.splice(index, 1);
  }

  reorderTags() {
    const selectedTags = this.filesTagList.filter(tag => this.fileTagIds.includes(tag.id));
    const unselectedTags = this.filesTagList.filter(tag => !this.fileTagIds.includes(tag.id));
    this.filesTagList = [...selectedTags, ...unselectedTags];
  }

  public onCancel(): void {
    this.onClose.next(false);
    this._bsModalRef.hide();
  }

  async getUploadedFilesInChunk(files: File, maxFileSizeMB: number = 200) {
    const file = files[0];
    // Create an array to store file chunks
    let fileChunks: File[] = [];
    let bufferChunkSize = maxFileSizeMB * (1024 * 1024); // Convert MB to bytes
    let fileStreamPos = 0;
    let endPos = bufferChunkSize;
    let size = file.size;
    let partCount = 0;
    let fileUploadTimeStamp = new Date().getTime();
    let isChunkUpload = false;

    // Split the file into chunks and create File instances
    while (fileStreamPos < size) {
      partCount++;
      const chunk = file.slice(fileStreamPos, endPos);
      const chunkFile = new File([chunk], `${file.name}.part_${partCount}`, {
        type: file.type
      });
      fileChunks.push(chunkFile);

      fileStreamPos = endPos;
      endPos = fileStreamPos + bufferChunkSize;
    }

    // Get total number of chunks
    const totalParts = fileChunks.length;

    // Function to upload chunks recursively
    const uploadChunkRecursively = (index: number, fileUploadTimeStamp: number) => {
      if (index >= totalParts) {
        this.alertService.showSuccessToast('File upload complete.');
        return;
      }
      const chunk = fileChunks[index];
      let filePartName;
      if (fileChunks.length > 1) {
        isChunkUpload = true;
        filePartName = `${file.name}.part_${index + 1}.${totalParts}`;
      } else {
        filePartName = `${file.name}`;
      }

      this.dropBoxService.uploadChunk(chunk, filePartName, isChunkUpload, fileChunks.length, index + 1, fileUploadTimeStamp).subscribe({
        next: () => {
          console.log(`Uploaded chunk ${index + 1} of ${totalParts}`);
          uploadChunkRecursively(index + 1, fileUploadTimeStamp); // Call the next upload
        },
        error: error => {
          console.error(`Error uploading chunk ${index + 1}:`, error);
        }
      });
    };
    uploadChunkRecursively(0, fileUploadTimeStamp);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    // this.destroy$.next();
    // this.destroy$.complete();
  }
}
