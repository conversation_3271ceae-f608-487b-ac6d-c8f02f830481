import { ChangeDetectorRef, Directive, Input, OnChanges } from '@angular/core';
import { AppConstants } from '../constants';

@Directive({
  selector: '[appImageLoaderDir]',
  host: {
    '[attr.src]': '_processedSrc',
    '[class.fadein]': '_applyFadeIn'
  }
})
export class ImageLoaderDirective implements OnChanges {
  @Input() src = '';
  @Input() appImageLoader = AppConstants.skeletonLoaderImage;
  @Input() appImagePlaceholder = '';
  _processedSrc!: string;
  _applyFadeIn: boolean = true;

  constructor(private readonly cdr: ChangeDetectorRef) {}
  ngOnChanges(): void {
    this.applyFadeIn(false);
    this._processedSrc = this.appImageLoader;
    const image = new Image();
    image.onerror = () => {
      this.applyFadeIn(false);
      this._processedSrc = this.appImagePlaceholder;
      this.applyFadeIn(true);
    };
    image.onload = () => {
      this.applyFadeIn(false);
      this._processedSrc = this.src;
      this.applyFadeIn(true);
    };
    image.src = this.src;
    this.applyFadeIn(true);
  }

  private applyFadeIn(apply: boolean): void {
    setTimeout(
      () => {
        this._applyFadeIn = true;
        this.cdr.detectChanges();
      },
      apply ? 300 : 0
    );
  }
}
