import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService } from '..';
import { ErrorMessages } from '../../@shared/constants';
import { ChangePassword } from '../../@shared/models/changePassword.model';
import { AlertService } from '../../@shared/services';

@Component({
  selector: 'sfl-reset-password',
  templateUrl: './reset-password.component.html',
  styles: []
})
export class ResetPasswordComponent implements OnInit {
  resetPass: ChangePassword = new ChangePassword();
  subscription: Subscription = new Subscription();
  httpclient: HttpClient;

  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly authService: AuthService,
    private readonly alertService: AlertService
  ) {
    this.route.queryParams.subscribe(params => {
      const re = /\ /gi;
      if (params.code) {
        this.resetPass.token = params.code.replace(re, '+');
      }
      this.resetPass.email = params.email;
    });
  }

  ngOnInit() {
    this.subscription.add(this.authService.checkResetPasswordTokenExpire(this.resetPass).subscribe());
  }

  resetPassword(): void {
    if (this.resetPass.password === this.resetPass.confirmPassword) {
      this.subscription.add(
        this.authService.resetPassword(this.resetPass).subscribe({
          next: res => {
            if (res) {
              if (res === true) {
                this.alertService.showSuccessToast('Password Change Successfully');
              } else {
                this.alertService.showErrorToast(ErrorMessages.badRequest);
              }
            }
            this.router.navigate(['login']);
          }
        })
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
