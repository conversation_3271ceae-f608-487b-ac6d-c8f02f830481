<div class="row mb-3">
  <div class="col-12">
    <div class="dropZone" ngFileDragDr (fileDropped)="getUploadedFilesInChunk($event)">
      <input
        type="file"
        #file
        accept="image/*, video/*, .doc, .docx,.pdf,.xls,.xlsx, .txt, .zip"
        multiple
        (change)="getUploadedFilesInChunk($event?.target?.files)"
      />
      <em class="fa fa-cloud-upload-alt text-primary iconimageupload" aria-hidden="true"></em>
      <h5 class="fw-bold">Drop a File To Attach</h5>
      <label style="text-transform: none" class="fw-bold">OR Click to Browse </label>
    </div>
  </div>
</div>
