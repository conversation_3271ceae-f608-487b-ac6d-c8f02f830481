import { WorkOrderSchedule } from '../../entities/workorder-management/workorder.model';
import { AppConstants } from '../constants';

export class WorkOrder {
  public id: number;
  public assementType: string;
  public frequncyType: string;
  public selectedWorkOrderId: number;
}

export class WorkOrderData {
  public id: number;
  public workOrderNumber: string;
  public isDeleted: boolean;
  public assesmentId: number;
  public fieldTech: number;
  public fieldTechList: FieldTechList[] = [];
  public fieldTechs: number[] = [];
  public dateScheduled: Date | string;
  public datePerformed: Date | string;
  public woStatus: number;
  public scanType: number;
  public reportAuther: string;
  public reportAuthorList: FieldTechList[] = [];
  public reportAuthors: <AUTHORS>
  public dueDate: Date | string;
  public reportStatus: string;
  public notes: string;
  public woStatusStr: string;
  public assesmentType: string;
  public userName: string;
  public expanded: boolean;
  public pdfReportLink: string;
  public pptReportLink: string;
  public fieldTech1: number;
  public datePerformeds: Date[] = [];
  public reportDocuments: ReportDocument[] = [];
  public reportId: string;
  public additionalScope: string;
  public jhaMap: string[] = [];
  public isReportGenreated: boolean;
  public workOrderSchedules: WorkOrderSchedule[] = [];
  public isScheduleExpanded: boolean = false;
  public tentativeMonth: number;
  public rescheduleDate: Date | string;
  public reportCompleteDate: Date | string;
  public reportCompleteDateDisplay: Date | string;
  public rescheduleCount: number;
  public workCompleteDate: Date | string;
  public workLogAuditHistory: WorkLogAuditHistory[] = [];
  public workCompleteDateDisplay: Date | string;
  public photosLink: string;
  public inValidUrl: boolean;
  public imageGalleryList: imageGalleryList[] = [];
  public videoGalleryList: VideoGalleryList[] = [];
  public qestFormCount: number;
  public failureRate: number;
  public zoneIds: number[] = [];
  public summaryReportCount: number;
  public tpmFormCount: number;
  public moduleTorqueCount: number;
  public siteName: string;
  public customerId: number;
  public portfolioId: number;
  public siteId: number;
  public enableCreateReport: boolean;
  public poNumber: string;
  public addOnCost: number;
  public cost: number;
  public isSummaryReportActive: boolean = false;
  public summaryReportDetails: InitialFormDetails = new InitialFormDetails();
  public isTPMFormActive: boolean = false;
  public tpmFormDetails: InitialFormDetails = new InitialFormDetails();
}

export class InitialFormDetails {
  public qestwoMapId: number = 0;
  public qestFormId: number = 0;
  public workOrderId: number = 0;
}

export class imageGalleryList {
  public totalCount: number;
  public imageGallery: ImageList[];
}
export class VideoGalleryList {
  public totalCount: number;
  public videoGallery: ImageList[];
}

export class ImageList {
  public id: number;
  public imageUrl: string;
  public thumbnailUrl: string;
  public customerId: number = null;
  public portfolioId: number = null;
  public siteId: number = null;
  public entityId: number = null;
  public entityNumber: string = '';
  public fileName: string = '';
  public conditionalTag: number[] = [];
  public conditionalTagTxt: string[] = [];
  public deviceTag: number[] = [];
  public deviceTagTxt: string[] = [];
  public fileTag: number[] = [];
  public fileTagTxt: string[] = [];
  public fileType: string = '';
  public notes: string;
  public createdBy: string = '';
  public createdDate: string = '';
  public isCustomerFacing: boolean;
  public isSelectedForPreview: boolean = false;
  public isPreviewImageEditTag: boolean = false;
}
export class WorkOrderAssFre {
  public assessmentId: number;
  public assesmentType: string;
  public frequncyType: string;
  public customerId: number;
  public customerNane: string;
  public portfolioId: number;
  public portfolioName: string;
  public siteId: number;
  public siteName: string;
  public isWorkOrderDeleted: boolean;
  public dropdownDtos = [];
  public zoneList = [];
  public workOrders: WorkOrderData[] = [];
  public oilElectricalReportDocuments: ReportDocument[] = [];
}

export class FieldTechList {
  public id: number;
  public workOrderId: number;
  public userId: number;
  public userName: string;
}

export class FieldTechs {
  public abbreviation: string;
  public id: number;
  public isActive: string;
  public name: string;
}

export class ReportDocument {
  public fileType: string;
  public fileUrl: string;
  public id: string;
  public isDeleted: false;
  public originalFileName: string;
  public reportId: string;
  public uploadedBy: number;
  public uploadedOn: string;
  public fileId: number;
}

export class WorkOrderFilter {
  public customerId: number;
  public portfolioId: number;
  public siteId: number;
  public startYear: number;
  public search: string;
  public page = 0;
  public sortBy = 'SiteName';
  public direction = 'asc';
  public itemsCount = +AppConstants.rowsPerPage;
}

export const WORKORDERFILTERLIST = {
  customer: 'Customer',
  portfolio: 'Portfolio',
  site: 'Site',
  startYear: 'StartYear'
};

export class MultipleFileUpload {
  public reportLabel: string;
  public reportId: string;
  public file: File = null;
  public isOilElectricalReport: boolean;
}

export class WorkLogAuditHistory {
  public action: string;
  public auditId: number;
  public auditLogDetails: [];
  public customerId: number;
  public entityId: number;
  public logDate: string;
  public portfolioId: number;
  public siteId: number;
  public userName: number;
  public workOrderId: number;
}
