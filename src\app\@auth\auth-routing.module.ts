import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthComponent } from './auth.component';
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';
import { LoginComponent } from './login/login.component';
import { ResetPasswordComponent } from './reset-password/reset-password.component';

const routes: Routes = [
  {
    path: '',
    component: AuthComponent,
    children: [
      {
        path: '',
        redirectTo: 'login',
        pathMatch: 'full',
        data: {
          pageTitle: 'Login',
          pageDescription: 'QE Solar - Login with email and password to get access'
        }
      },
      {
        path: 'login',
        component: LoginComponent,
        data: {
          pageTitle: 'Login',
          pageDescription: 'QE Solar - Login with email and password to get access'
        }
      },
      {
        path: 'forgot-password',
        component: ForgotPasswordComponent,
        data: {
          pageTitle: 'Forgor Password',
          pageDescription: "Enter your email address and we'll send a link to reset your password"
        }
      },
      {
        path: 'reset-password',
        component: ResetPasswordComponent,
        data: {
          pageTitle: 'Reset Password',
          pageDescription: 'Reset your password'
        }
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AuthRoutingModule {}
