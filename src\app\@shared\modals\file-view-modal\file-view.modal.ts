import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Subject } from 'rxjs';

@Component({
  selector: 'sfl-file-view',
  templateUrl: './file-view-modal.html'
})
export class FileViewComponent implements OnInit, OnDestroy {
  public onClose: Subject<boolean>;
  @Input() filePath: string;
  trustedFilePath;

  constructor(public readonly _bsModalRef: BsModalRef, protected _sanitizer: DomSanitizer) {}

  public ngOnInit(): void {
    this.onClose = new Subject();
    if (this.filePath) {
      this.filePath += '#toolbar=0';
      this.trustedFilePath = this._sanitizer.bypassSecurityTrustResourceUrl(this.filePath);
    }
  }

  public onCancel(): void {
    this.onClose.next(false);
    this._bsModalRef.hide();
  }

  // Destroy
  ngOnDestroy() {
    if (this._bsModalRef) {
      this._bsModalRef.hide();
    }
  }
}
