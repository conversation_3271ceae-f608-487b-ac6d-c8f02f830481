.close {
  border: none;
  background-color: transparent;
}

.modal-full-view-dialog {
  margin: 0px !important;
  min-width: 100vw;
  min-height: 100vh;
  max-width: 100% !important;
  .modal-content {
    padding: 10px 30px 15px 30px;
    min-height: 100vh;
    min-width: 100vw;
  }
}
.drop-box-header {
  .file-upload-btn {
    background-color: #36f;
    border-color: #36f;
    color: #fff;
    padding: 0.48rem 0.875rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    text-transform: uppercase;
    line-height: 1rem;
    font-weight: bold;
  }
}

.drop-box-body {
  min-height: calc(100vh - 180px);
  .right-border {
    border-right: 1px solid #2c3853;
  }
  .image-preview {
    width: 100%;
    position: relative;
    min-width: 90%;
    img {
      width: 100%;
      display: block;
      object-fit: cover;
      aspect-ratio: 3/2;
      border-radius: 8px;
    }
    .image-top-info {
      position: absolute;
      top: 10px;
      left: 10px;
      .info-badge {
        background-color: #232c45;
        padding: 2px 8px;
        border-radius: 5px;
        p {
          font-size: 12px;
          color: #ffffff;
        }
      }
    }
    .expand-icon {
      position: absolute;
      top: 10px;
      right: 10px;

      .expand-img {
        width: 38px;
        height: 36px;
      }
    }
    .name-preview-btn {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      background-color: rgba(0, 0, 0, 0.6);
      color: white;
      padding: 6px;
      box-sizing: border-box;
      text-align: center;
      justify-content: space-between;
      display: flex;
      border-radius: 5px;
    }
    .image-name {
      color: #ffffff;
      padding-top: 10px;
    }
  }
  .images-list-main {
    .filter-border {
      border: 1px solid #2c3853;
      padding: 8px;
    }
  }
  .tag-info-badge {
    color: #ffffff;
    background-color: #0f172c;
    padding: 3px 6px;
    border-radius: 5px;
    font-size: 12px;
  }
  .image-listing {
    .fix-listing-area {
      max-height: 500px;
      min-height: 500px;
      overflow-y: auto;

      .solar-image {
        position: relative;
        img {
          aspect-ratio: 3 / 2;
          width: 100%;
          object-fit: cover;
          border: 2px dashed black;

          &.previewed {
            border: 2px solid #36f;
          }
        }
        .single-image-checkbox {
          position: absolute;
          top: 5%;
          left: 5%;
          transform: translate(-5%, -5%);
        }
        .single-image-date {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          background-color: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 3px;
          box-sizing: border-box;
          text-align: center;
          border-radius: 5px;
        }
      }
    }
    img {
      width: 100%;
      border-radius: 8px;
    }
  }
}
::ng-deep .appSpinner_gallery nb-spinner {
  align-items: center !important;
  padding: 7% !important;
}
