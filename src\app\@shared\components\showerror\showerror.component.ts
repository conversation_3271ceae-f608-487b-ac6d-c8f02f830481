import { Component, Input, OnInit } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';

@Component({
  selector: 'sfl-showerror',
  templateUrl: './showerror.component.html',
  styleUrls: ['./showerror.component.scss']
})
export class ShowerrorComponent implements OnInit {
  @Input() message;

  constructor(public _bsModalRef: BsModalRef) {}

  public ngOnInit(): void {}

  public onCancel(): void {
    this._bsModalRef.hide();
  }
}
