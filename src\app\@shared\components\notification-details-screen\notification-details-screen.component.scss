.modal-body {
  padding: 0 !important;
}
.notification-details {
  position: relative;
  height: 89vh;

  @media (max-width: 700px) {
    .modal-dialog-right {
      width: 80%;
    }
  }

  .notification-header {
    border-bottom: 1px solid #2c3853;
    .mark-read-link {
      cursor: pointer;
      font-size: 12px;
      .mark-all {
        color: #598bff;
      }
    }
    .small-expand {
      width: auto;
      .expand-icon {
        display: none;
      }
    }
    @media only screen and (max-width: 550px) {
      flex-wrap: wrap;
      .small-expand {
        width: 100%;
        .expand-icon {
          display: block;
        }
      }
      .small-hidden-icon {
        display: none;
      }
    }
  }

  .list-notification {
    max-height: 64vh;
    overflow-y: auto;

    .list-group-item {
      background-color: transparent !important;
      border-bottom: 1px solid #2c3853 !important;

      &:hover {
        background-color: #2c3853 !important;
        border-color: white;
        & p {
          color: #fff;
        }
        & a {
          color: #fff;
        }
        & div {
          color: #fff;
        }
        .dropdown-divider {
          border-color: white !important;
        }
      }
      .description {
        font-size: 14px;
      }
      .user_info {
        border-radius: 50%;
        font-weight: bolder;
        text-align: center;
        width: 40px;
        height: 40px;
        padding: 10px;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #151a30;
      }
      .user_date {
        font-size: 12px;
        font-weight: 300;
      }
      .details {
        .more-details-btn {
          background: #3366ff;
          color: white;
          padding: 0px 5px;
          margin-left: 10px;
        }
      }

      .dropdown-divider {
        border-bottom: 1px solid #2c3853 !important;
        margin: 0px 0px 8px 0px;
      }
    }
  }
}

::ng-deep.notification-dialog {
  .modal-content {
    padding: 0;
    min-height: 89vh;
  }
}
