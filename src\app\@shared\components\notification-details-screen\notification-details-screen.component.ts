import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Subject, Subscription } from 'rxjs';
import { AppConstants } from '../../constants';
import { AllNotificationModal, NotificationActionSummary } from '../../models/user.model';
import { NotificationService } from '../../services/notification.service';
import { StorageService } from '../../services/storage.service';

@Component({
  selector: 'sfl-notification-details-screen',
  templateUrl: './notification-details-screen.component.html',
  styleUrls: ['./notification-details-screen.component.scss']
})
export class NotificationDetailsScreenComponent implements OnInit {
  total: number;
  notificationsList: AllNotificationModal[];
  selectedUnreadNotifications: AllNotificationModal[] = [];
  notificationActions: NotificationActionSummary[] = [];
  subscription: Subscription = new Subscription();
  NotificationLoading = false;
  isAnyCheckboxChecked = false;
  pageSize = 10;
  currentPage = 1;
  loggedUser;
  dateTimeFormat = AppConstants.dateTimeFormat;
  profileRandomBgColor = '';
  notificationParams = {
    triggerId: null,
    userId: null,
    showUnreadOnly: false,
    sortBy: 'notificationDate',
    direction: 'desc',
    page: 0,
    itemsCount: 10
  };
  public onClose: Subject<boolean> = new Subject();
  constructor(
    public _bsModalRef: BsModalRef,
    private readonly storageService: StorageService,
    private readonly notificationService: NotificationService,
    private readonly router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loggedUser = this.storageService.get('user');
    this.notificationParams = {
      triggerId: null,
      userId: this.loggedUser.userId,
      showUnreadOnly: true,
      sortBy: 'notificationDate',
      direction: 'desc',
      page: 0,
      itemsCount: 10
    };
    this.profileRandomBgColor = this.notificationService.getRandomColor();
    this.getAllNotification();
  }

  closeNotificationModal() {
    this.onClose.next(false);
    this._bsModalRef.hide();
  }

  refreshListOnToggleChange() {
    this.currentPage = 1;
    this.pageSize = 10;
    this.notificationParams.itemsCount = 10;
    const getListWithDefaultParam = {
      ...this.notificationParams,
      page: 0,
      itemsCount: 10
    };
    this.getAllNotification(getListWithDefaultParam);
  }

  getAllNotification(params = this.notificationParams) {
    this.NotificationLoading = true;
    this.subscription.add(
      this.notificationService.getAllNotification(params).subscribe({
        next: res => {
          this.notificationsList = res.notifications.map(items => {
            return {
              ...items,
              isUnread: !items.isRead,
              isMoreDetails: false
            };
          });
          this.isAnyUnreadNotification();
          this.total = res.total;
          this.selectedUnreadNotifications = this.notificationsList.filter(item => item.isUnread);
          this.getUpdatedUnreadCount();
          this.NotificationLoading = false;
          this.cdr.detectChanges();
        },
        error: err => {
          this.NotificationLoading = false;
        }
      })
    );
  }

  getUpdatedUnreadCount() {
    this.subscription.add(
      this.notificationService.getUpdatedNotificationCount(this.loggedUser.userId).subscribe({
        next: res => {
          this.notificationService.notificationCount$.next(res);
        },
        error: err => {
          this.NotificationLoading = false;
        }
      })
    );
  }

  navigateToEntity(notification: AllNotificationModal) {
    if (!notification.isDeleted) {
      const readUnreadParams = {
        userNotificationId: [notification.userNotificationId],
        isRead: true,
        isReadFromWeb: true
      };

      const navigateToWorkOrder = () => {
        const { assessmentType: assementType, frequencyType, entityId: id } = notification;
        this.router.navigate(['/entities/workorders/add'], {
          queryParams: { id, assementType, frequencyType }
        });
      };

      const navigateToTicketDetail = () => {
        this.router.navigateByUrl(`/entities/ticket/detail/view/${notification.ticketNumber}`);
      };

      const navigateToViewSiteCheckInCheckOut = () => {
        this.router.navigateByUrl(`/entities/safety/site-checkin/detail/view/${notification.entityId}`);
      };

      const navigateToAlert = () => {
        const url = `/entities/performance/outage?customerId=${notification.customerId}&siteId=${notification.siteId}&portfolioId=${notification.portfolioId}&date=${notification.notificationDate}`;
        this.router.navigateByUrl(url);
      };

      const markNotificationAsRead = () => {
        this.subscription.add(
          this.notificationService.markAsReadUnRead(readUnreadParams).subscribe({
            next: res => {
              this.NotificationLoading = false;
              this.selectedUnreadNotifications = [];
              this.getAllNotification();
            },
            error: err => {
              this.NotificationLoading = false;
            }
          })
        );
      };

      if (!notification.isRead) {
        markNotificationAsRead();
      }

      if (notification.triggerId === 1 || notification.triggerId === 15) {
        navigateToWorkOrder();
      } else if (notification.triggerId === 16 || notification.triggerId === 17) {
        navigateToViewSiteCheckInCheckOut();
      } else if (notification.triggerId === 18 || notification.triggerId === 20) {
        navigateToAlert();
      } else {
        navigateToTicketDetail();
      }
      this.closeNotificationModal();
    }
  }

  isAnyUnreadNotification() {
    this.isAnyCheckboxChecked = this.notificationsList.some(item => item.isUnread);
  }

  updateCheckboxStatus(userNotificationId: number, isRead: boolean) {
    this.isAnyUnreadNotification();
    const readUnreadParams = {
      userNotificationId: [userNotificationId],
      isRead: !isRead,
      isReadFromWeb: true
    };
    this.markSelectedAsReadUnRead(readUnreadParams);
  }

  markSelectedAsRead() {
    this.NotificationLoading = true;
    this.selectedUnreadNotifications = this.notificationsList.filter(item => item.isUnread);
    const notificationId = this.selectedUnreadNotifications.map(item => item.userNotificationId);
    const readUnreadParams = {
      userNotificationId: notificationId,
      isRead: true,
      isReadFromWeb: true
    };
    this.markSelectedAsReadUnRead(readUnreadParams, true);
  }

  markSelectedAsReadUnRead(readUnreadParams, isPageReset = false) {
    this.subscription.add(
      this.notificationService.markAsReadUnRead(readUnreadParams).subscribe({
        next: res => {
          this.NotificationLoading = false;
          this.selectedUnreadNotifications = [];
          if (isPageReset) {
            this.notificationParams.page = 0;
            this.currentPage = 1;
          }
          this.getAllNotification();
        },
        error: err => {
          this.NotificationLoading = false;
        }
      })
    );
  }

  getInitials(name: string | undefined): string {
    if (name) {
      const nameArray: string[] = name.split(' ');
      const firstLetterOfName = nameArray[0]?.charAt(0);
      const secondLetterOfName = nameArray[nameArray.length - 1]?.charAt(0);
      if (nameArray.length > 1) {
        return `${firstLetterOfName || ''}${secondLetterOfName || ''}`.toUpperCase();
      } else {
        return (firstLetterOfName || '').toUpperCase();
      }
    }
    return '';
  }

  toggleMoreDetails(auditActionId: number, isMoreDetails) {
    this.NotificationLoading = true;
    const clickedNotification = this.notificationsList.find(item => item.auditActionId === auditActionId);
    if (isMoreDetails) {
      this.toggleMoreDetailsIcon(auditActionId, clickedNotification);
      this.notificationActions = [];
      return;
    }
    if (clickedNotification && clickedNotification.auditActionId) {
      this.subscription.add(
        this.notificationService.getMoreDetailsOfNotification(clickedNotification).subscribe({
          next: res => {
            if (res && res.length) {
              const actionSummary = JSON.parse(res);
              for (const j of actionSummary) {
                j.Value = JSON.parse(j.Value);
              }
              this.notificationActions = actionSummary;
              this.toggleMoreDetailsIcon(auditActionId, clickedNotification);
            } else {
              this.notificationActions = [];
              this.toggleMoreDetailsIcon(auditActionId, clickedNotification);
            }
          },
          error: err => {
            this.NotificationLoading = false;
          }
        })
      );
    } else {
      this.notificationActions = [];
      this.toggleMoreDetailsIcon(auditActionId, clickedNotification);
    }
  }

  toggleMoreDetailsIcon(auditActionId, clickedNotification) {
    clickedNotification.isMoreDetails = !clickedNotification.isMoreDetails;
    this.notificationsList.forEach(item => {
      if (item.auditActionId !== auditActionId) {
        item.isMoreDetails = false;
      }
    });
    this.NotificationLoading = false;
  }

  onChangeSize() {
    this.currentPage = 0;
    this.notificationParams.page = 0;
    this.notificationParams.itemsCount = Number(this.pageSize);
    this.getAllNotification();
  }

  onPageChange(obj) {
    this.currentPage = obj;
    this.notificationParams.page = this.currentPage - 1;
    this.getAllNotification();
  }

  getInitialsColorForBgColor(bgColor: string) {
    return this.notificationService.getInitialsColorForBgColor(bgColor);
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
