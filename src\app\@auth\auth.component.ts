import { Location } from '@angular/common';
import { Component } from '@angular/core';
import { NbAuthComponent, NbAuthService } from '@nebular/auth';
import { AppSettings } from '../@shared/models/user.model';
import { environment } from './../../environments/environment';
import { AuthService } from './auth.service';

@Component({
  selector: 'auth',
  styles: [],
  template: `
    <nb-layout>
      <nb-layout-column class="d-flex justify-content-center flex-wrap align-content-around">
        <nb-card>
          <nb-card-body>
            <div class="h-100 d-flex justify-content-center flex-column">
              <div class="d-flex justify-content-center align-items-center">
                <nb-auth-block>
                  <router-outlet></router-outlet>
                </nb-auth-block>
              </div>
              <div *ngIf="versionApi">
                <span class="mb-1 version_font pull-right"
                  >QE Solar Tools (v{{ versionApi.webPortalVersion }} | API: v{{ versionApi.webApiVersion }})</span
                >
              </div>
            </div>
          </nb-card-body>
        </nb-card>
      </nb-layout-column>
    </nb-layout>
  `
})
export class AuthComponent extends NbAuthComponent {
  versionApi: AppSettings;
  webVersion: string;

  constructor(auth: NbAuthService, location: Location, private readonly authService: AuthService) {
    super(auth, location);
    this.getVersion();
  }

  ngOnInit() {
    this.webVersion = environment.webVersion;
  }

  getVersion() {
    this.authService.getVersAccount().subscribe({
      next: res => {
        this.versionApi = res;
      }
    });
  }
}
