export interface ImageTransform {
  scale?: number;
  rotate?: number;
  flipH?: boolean;
  flipV?: boolean;
}

export enum ImageMimeType {
  PNG = 'image/png',
  JPEG = 'image/jpeg',
  JPG = 'image/jpg',
  GIF = 'image/gif',
  WEBP = 'image/webp',
  BMP = 'image/bmp',
  APNG = 'image/apng', // no support
  TIFF = 'image/tiff', // no support
  TIF = 'image/tif', // no support
  XICON = 'image/x-icon',
  ICO = 'image/ico',
  SVG = 'image/svg+xml'
}

export const ImageTransformFormat = {
  [ImageMimeType.PNG]: 'png',
  [ImageMimeType.JPEG]: 'jpeg',
  [ImageMimeType.JPG]: 'jpeg',
  [ImageMimeType.GIF]: 'jpeg',
  [ImageMimeType.WEBP]: 'webp',
  [ImageMimeType.BMP]: 'bmp',
  [ImageMimeType.APNG]: 'jpeg', // Mapped as 'jpeg' intentionally
  [ImageMimeType.TIFF]: 'jpeg', // Mapped as 'jpeg' intentionally
  [ImageMimeType.TIF]: 'jpeg', // Mapped as 'jpeg' intentionally
  [ImageMimeType.XICON]: 'ico',
  [ImageMimeType.ICO]: 'ico',
  [ImageMimeType.SVG]: 'jpeg'
};

export const ImageTransformMimeType = {
  [ImageMimeType.PNG]: 'image/png',
  [ImageMimeType.JPEG]: 'image/jpeg',
  [ImageMimeType.JPG]: 'image/jpeg',
  [ImageMimeType.GIF]: 'image/jpeg',
  [ImageMimeType.WEBP]: 'image/webp',
  [ImageMimeType.BMP]: 'image/bmp',
  [ImageMimeType.APNG]: 'image/jpeg', // Mapped as 'jpeg' intentionally
  [ImageMimeType.TIFF]: 'image/jpeg', // Mapped as 'jpeg' intentionally
  [ImageMimeType.TIF]: 'image/jpeg', // Mapped as 'jpeg' intentionally
  [ImageMimeType.XICON]: 'image/ico',
  [ImageMimeType.ICO]: 'image/ico',
  [ImageMimeType.SVG]: 'image/jpeg'
};
