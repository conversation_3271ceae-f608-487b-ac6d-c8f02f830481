<div class="notification-details p-2" [nbSpinner]="NotificationLoading">
  <div class="d-flex flex-column h-100 justify-content-between">
    <div>
      <div class="notification-header p-3 bg-light">
        <div class="d-flex align-items-center justify-content-between w-100">
          <div class="d-flex small-expand align-items-center justify-content-between">
            <p class="mb-2">Notifications</p>
            <p class="mb-2 expand-icon">
              <em aria-hidden="true" class="fa-solid fa-xmark fa-xl text-primary cursor-pointer" (click)="closeNotificationModal()"></em>
            </p>
          </div>
          <div class="d-flex gap-3 align-items-center justify-content-center">
            <p class="mb-2 d-flex align-items-center justify-content-between gap-3">
              <label class="label mb-0 align-items-center justify-content-between gap-2 d-flex" for="customer">
                Only show unread
                <nb-toggle
                  [(checked)]="notificationParams.showUnreadOnly"
                  labelPosition="start"
                  (checkedChange)="refreshListOnToggleChange()"
                ></nb-toggle>
              </label>
            </p>
            <p class="mb-2 small-hidden-icon">
              <em aria-hidden="true" class="fa-solid fa-xmark fa-xl text-primary cursor-pointer" (click)="closeNotificationModal()"></em>
            </p>
          </div>
        </div>
        <p *ngIf="isAnyCheckboxChecked" class="mb-0 text-end mark-read-link">
          <a href="javascript: void(0);" class="pe-auto mark-all" (click)="markSelectedAsRead()">Mark selected as read</a>
        </p>
      </div>
      <div class="list-notification">
        <ul class="list-group list-group-flush">
          <li
            class="list-group-item"
            *ngFor="
              let item of notificationsList
                | paginate
                  : {
                      itemsPerPage: notificationParams.itemsCount,
                      currentPage: currentPage,
                      totalItems: total
                    };
              let i = index
            "
          >
            <div class="d-flex align-items-center">
              <div
                *ngIf="item.triggerId !== 18 && item.triggerId !== 19 && item.triggerId !== 20"
                class="user_info me-2"
                [ngStyle]="{
                  'background-color': item.profileBackgroundColor ? item.profileBackgroundColor : profileRandomBgColor,
                  color: item.profileBackgroundColor
                    ? getInitialsColorForBgColor(item.profileBackgroundColor)
                    : getInitialsColorForBgColor(profileRandomBgColor)
                }"
              >
                <span>
                  <sfl-user-avatar
                    [photoUrl]="item.profileImage"
                    [width]="'40px'"
                    [height]="'40px'"
                    [initials]="getInitials(item.user)"
                  ></sfl-user-avatar>
                </span>
              </div>
              <div class="w-100 me-2">
                <div class="d-flex align-items-center justify-content-between">
                  <a href="javascript: void(0);" class="mb-1 fw-bold pe-auto" (click)="navigateToEntity(item)">{{ item.title }}</a>
                  <p class="mb-1 ms-2">
                    <nb-checkbox
                      [(ngModel)]="item.isUnread"
                      (change)="updateCheckboxStatus(item.userNotificationId, item.isRead)"
                    ></nb-checkbox>
                  </p>
                </div>
                <p class="mb-1 description">{{ item.description }}</p>
                <div class="d-flex align-items-center justify-content-between mt-2">
                  <p class="mb-1 me-3 user_date">{{ item.user || '' }}</p>
                  <p class="mb-1 details">
                    {{ item.notificationDate | date : dateTimeFormat }}
                    <button
                      *ngIf="item?.auditActionId > 0"
                      class="btn more-details-btn"
                      nbTooltip="{{ item.isMoreDetails ? 'Show less' : 'Show more' }}"
                      nbTooltipPlacement="top"
                      nbTooltipStatus="primary"
                      (click)="toggleMoreDetails(item.auditActionId, item.isMoreDetails)"
                    >
                      <em [ngClass]="item.isMoreDetails ? 'fa-solid fa-caret-up' : 'fa-solid fa-caret-down'"></em>
                    </button>
                  </p>
                </div>
                <div>
                  <ng-container *ngIf="notificationActions?.length && item.isMoreDetails">
                    <div class="action-summary">
                      <div class="row auditActionSummary pb-1">
                        <div class="col-2"><label class="label mb-0">Field</label></div>
                        <div class="col-5 text-center"><label class="label mb-0">Original Value</label></div>
                        <div class="col-5 text-center"><label class="label mb-0">New Value</label></div>
                      </div>
                      <hr class="dropdown-divider" />
                      <div class="mt-1" *ngFor="let actionSummary of notificationActions">
                        <div class="row">
                          <div class="col-2">{{ actionSummary?.Label }}</div>
                          <div class="col-5 text-center">{{ actionSummary?.Value?.Old || '-' }}</div>
                          <div class="col-5 text-center">{{ actionSummary?.Value?.New || '-' }}</div>
                        </div>
                      </div>
                    </div>
                  </ng-container>
                  <ng-container class="auditActionSummary" *ngIf="!notificationActions?.length && item.isMoreDetails">
                    <div class="d-flex align-items-center justify-content-center">
                      <p>No Field Updated</p>
                    </div>
                  </ng-container>
                </div>
              </div>
            </div>
          </li>
        </ul>
        <div *ngIf="!notificationsList?.length" class="d-flex align-items-center justify-content-center mt-4">
          <p>No Data Found</p>
        </div>
      </div>
    </div>
    <div class="modal-footer mt-2 d-flex align-items-center justify-content-between bg-light p-3" *ngIf="notificationsList?.length">
      <div class="d-flex align-items-center">
        <label class="mb-0">Items per page: </label>
        <ng-select class="ms-2" [(ngModel)]="pageSize" [clearable]="false" [searchable]="false" (change)="onChangeSize()">
          <ng-option value="5">5</ng-option>
          <ng-option value="10">10</ng-option>
          <ng-option value="50">50</ng-option>
          <ng-option value="100">100</ng-option>
        </ng-select>
        <strong class="ms-md-3">Total: {{ total }}</strong>
      </div>
      <div class="ms-md-auto ms-sm-0">
        <pagination-controls (pageChange)="onPageChange($event)" class="paginate"></pagination-controls>
      </div>
    </div>
  </div>
</div>
