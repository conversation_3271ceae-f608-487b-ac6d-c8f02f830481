export class NonConformanceDto {
  public ncGuid: string;
  public reportGuid: string;
  public order: number;
  public isResolve = false;
  public isUrgent: boolean;
  public isIssueEditable: boolean;
  public isActionEditable: boolean;
  public component: number;
  public priority: number;
  public ticketType: number;
  public componentStr: string;
  public ticketNumber: string;
  public location: string;
  public issue: string;
  public actions: string;
  public imageId: string;
  public nCIId: string;
  public componentList: NonConformanceComponentList[];
  public images: NonConformanceImagesById[];
  public isSelected: boolean;
}

export class NonConformanceComponentList {
  public abbreviation: string;
  public id: number;
  public isSelected: boolean;
  public name: string;
  public sectionAbbreviation: string;
  public sectionId: number;
  public sectionName: string;
}

export class NonConformanceImagesById {
  public fileImage: File;
  public reportId: string;
  public imageUrl: string;
  public isIncludeinReport: boolean;
  public originalImage: string;
  public groupId: number;
  public ncGuid: string;
  public nciGuid: string;
  public imageGuid: string;
  public order: number;
  public type: string;
}

export const TicketPriorityMapping = [
  { id: 1, name: 'Low' },
  { id: 2, name: 'Medium' },
  { id: 3, name: 'High' },
  { id: 4, name: 'Safety' }
];

export const TicketTypeMapping = [
  { id: 1, name: 'CM' },
  { id: 2, name: 'Snow' },
  { id: 3, name: 'Vegetation' },
  { id: 4, name: 'Non-conformance' }
];

export class CreateNCBulkTicket {
  workOrderId: Number = 0;
  reportId: string;
  ncData: BulkNCTicketDto[];
}

export class BulkNCTicketDto {
  ncGuid: string;
  order: Number;
  component: Number;
  componentStr: string;
  issue: string;
  location: string;
  actions: string;
  priority: Number;
  ticketType: Number;
  isNCItemUpdated: boolean = false;
}

export interface BulkNCTicketCreateResponse {
  status: Number; //int // 0 -- Fail, 1 -- Success
  message: string;
  id: string;
  entryid: number;
  resultTicket: [
    {
      ticketId: number;
      ncGuid: string;
      ticketNumber: string;
    }
  ];
}
