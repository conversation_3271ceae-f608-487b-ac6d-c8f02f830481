.ticketFilter {
  nb-card {
    margin: 0 !important;
  }
  .filter-section {
    width: 100%;

    .applied-filter {
      ul {
        margin-block-end: 0 !important;
        margin-block-start: 0 !important;
        padding-inline-start: 0 !important;
        list-style-type: none;

        li {
          padding: 3px 5px;
          background: #161b2f;
          width: -webkit-fill-available;
          nb-icon {
            cursor: pointer;
          }
        }
      }
    }

    .filter-icon {
      color: #3366ff;
      cursor: pointer;
    }
  }
  .lbl-archive {
    margin-bottom: 0.8rem;
  }
  .archive-toggle {
    display: flex;
    align-items: center;
  }
}

.deviceSearch {
  font-size: 0.9rem;
}

.ticketStatus {
  padding: 4px 10px;
  background-color: #ecf1ff;
  border-radius: 3px;
  color: #3366ff;
  font-weight: bold;
}

.badge-pill {
  font-weight: bold;
  padding: 4px 10px;
  color: white;
  font-size: 0.9375rem;
}

.badge-warning {
  background-color: #f0c108;
}

.badge-danger {
  background-color: #c40e0e;
}

.ticketOpenStatus {
  padding: 4px 10px;
  background-color: #ecf1ff;
  border-radius: 3px;
  color: #3366ff;
  font-weight: bold;
}

.ticketCloseStatus {
  padding: 4px 10px;
  background-color: #e4ffe0;
  border-radius: 3px;
  color: #008f26;
  font-weight: bold;
}

.ticketPendingStatus {
  padding: 4px 10px;
  background-color: #fef6d3;
  border-radius: 3px;
  color: #d8b106;
  font-weight: bold;
}

.ticketLowPriority {
  color: #58625f;
  font-weight: bold;
}

.ticketHighPriority {
  color: #fe1f00;
  font-weight: bold;
}

.ticketMediumPriority {
  color: #ff9f00;
  font-weight: bold;
}

.max-lines {
  text-overflow: ellipsis;
  overflow: hidden;
  line-height: 25px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  font-size: 14px !important;
}
.w-180 {
  width: 180px;
}

::ng-deep .ticketSpinner nb-spinner {
  align-items: flex-start !important;
  padding: 8% !important;
}

::ng-deep nb-card-header {
  padding: 0.75rem 1.25rem !important;
}

.pointerTicketNumberLink {
  color: rgb(89, 139, 255) !important;
  cursor: pointer !important;
  text-decoration-line: underline;
}
.minWidth {
  min-width: max-content;
  max-width: max-content;
}
.marginTop {
  margin-top: -10px;
}
@media only screen and (max-width: 768px) {
  li.minWidth {
    min-width: auto;
  }
}
.exclusive-toggle {
  display: flex;
  justify-content: end;
  margin: 10px 0;
}
