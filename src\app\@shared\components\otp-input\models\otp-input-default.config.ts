export interface SflOtpInputComponentOptions {
  otpLength?: number;
  autoFocus?: boolean;
  autoBlur?: boolean;
  hideInputValues?: boolean;
  showInputValuesBtn?: boolean;
  regexp?: RegExp;
  showBlinkingCursor?: boolean;
  ariaLabels?: string[];
  inputMode?: string;
}

export const defaultOptions: SflOtpInputComponentOptions = {
  otpLength: 6,
  autoFocus: true,
  autoBlur: true,
  hideInputValues: false,
  showInputValuesBtn: false,
  regexp: /^[0-9]+$/,
  showBlinkingCursor: true,
  ariaLabels: [],
  inputMode: 'numeric'
};

export class SflOtpInputComponentOptionsClass implements SflOtpInputComponentOptions {
  otpLength: number;
  autoFocus: boolean;
  autoBlur: boolean;
  hideInputValues: boolean;
  showInputValuesBtn: boolean;
  regexp: RegExp;
  showBlinkingCursor: boolean;
  ariaLabels: string[];
  inputMode: string;
  constructor(predefinedOptions: Partial<SflOtpInputComponentOptions>) {
    this.otpLength = predefinedOptions.otpLength ?? defaultOptions.otpLength;
    this.autoFocus = predefinedOptions.autoFocus ?? defaultOptions.autoFocus;
    this.autoBlur = predefinedOptions.autoBlur ?? defaultOptions.autoBlur;
    this.hideInputValues = predefinedOptions.hideInputValues ?? defaultOptions.hideInputValues;
    this.showInputValuesBtn = predefinedOptions.showInputValuesBtn ?? defaultOptions.showInputValuesBtn;
    this.regexp = predefinedOptions.regexp ?? defaultOptions.regexp;
    this.showBlinkingCursor = predefinedOptions.showBlinkingCursor ?? defaultOptions.showBlinkingCursor;
    this.ariaLabels = predefinedOptions.ariaLabels ?? defaultOptions.ariaLabels;
    this.inputMode = predefinedOptions.inputMode ?? defaultOptions.inputMode;
  }
}
