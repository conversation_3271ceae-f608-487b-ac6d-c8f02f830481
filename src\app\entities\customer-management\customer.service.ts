import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { from, Observable } from 'rxjs';
import { ApiUrl } from '../../@shared/constants';
import { Customer } from '../../@shared/models/customer.model';
import { Dropdown } from '../../@shared/models/dropdown.model';
import { PortfolioService } from '../portfolio-management/portfolio.service';
import { SiteService } from '../site-management/site.service';

@Injectable({
  providedIn: 'root'
})
export class CustomerService {
  customerList: Dropdown[] = [];
  constructor(
    private readonly http: HttpClient,
    private readonly siteService: SiteService,
    private readonly portfolioService: PortfolioService
  ) {}

  createCustomer(customer: Customer): Observable<any> {
    this.customerList = [];
    return this.http.post(ApiUrl.CREATE_CUSTOMER, customer);
  }

  getAllCustomersByfilter(obj): Observable<any> {
    return this.http.post(ApiUrl.CUSTOMERS_BY_FILTER, obj);
  }

  getAlertOutageDetail(obj): Observable<any> {
    return this.http.post(ApiUrl.CUSTOMER_OUTAGE, obj);
  }

  preserveAlertOutageDetail(obj): Observable<any> {
    return this.http.post(ApiUrl.ADD_UPDATE_CUSTOMER_OUTAGE, obj);
  }

  getById(id) {
    return this.http.get(`${ApiUrl.GET_BY_ID_CUSTOMER}${id}`);
  }

  updateCustomer(customer: Customer): Observable<any> {
    this.customerList = [];
    return this.http.put(ApiUrl.UPDATE_CUSTOMER, customer);
  }

  deleteCustomer(id): Observable<any> {
    this.customerList = [];
    this.siteService.siteList = [];
    this.portfolioService.portfolioList = [];
    return this.http.delete(`${ApiUrl.DELETE_CUSTOMERS}${id}`);
  }

  clearCustomer() {
    this.customerList = [];
  }

  getAllCustomer(isArchive: boolean = false): Observable<any> {
    const response = new Promise((resolve, reject) => {
      if (this.customerList.length > 0) {
        debugger
        const filteredList = isArchive ? this.customerList : this.customerList.filter(item => !item.isArchive);
        resolve(filteredList);
      } else {
        this.http.get(ApiUrl.GET_CUSTOMER).subscribe({
          next: (res: Dropdown[]) => {
            debugger;
            this.customerList = res;
            const filteredList = isArchive ? this.customerList : this.customerList.filter(item => !item.isArchive);
            resolve(filteredList);
          },
          error: e => {
            reject(e);
          }
        });
      }
    });
    return from(response);
  }

  getSiteAuditJHACustomersAll(): Observable<any> {
    return this.http.get(`${ApiUrl.GET_SITE_AUDIT_CUSTOMER}?IsSiteAuditJHA=true`);
  }

  getSiteAuditReportCustomersAll(): Observable<any> {
    return this.http.get(`${ApiUrl.GET_SITE_AUDIT_REPORT_CUSTOMER}?IsSiteAuditJHA=false`);
  }

  getAvailCustomer(): Observable<any> {
    return this.http.get(`${ApiUrl.GET_AVAILIBILITY_CUSTOMER}?isCheckAvailability=true`);
  }

  getPRGenerateLogs(id: number): Observable<any> {
    return this.http.get(`${ApiUrl.GET_PR_GENERATE_LOGS}/${id}`);
  }

  checkAutoMapPRWorkOrder(id: number): Observable<any> {
    return this.http.get(`${ApiUrl.CHECK_AUTO_MAP_PR_WORK_ORDER}/${id}`);
  }

  getCustomerArchive(id): Observable<any> {
    return this.http.get(`${ApiUrl.GET_CUSTOMER_ARCHIVE}/${id}`);
  }

  updateCustomerArchiveStatus(UpdatedArchiveData: any): Observable<any> {
    return this.http.post(`${ApiUrl.UPDATE_CUSTOMER_ARCHIVE_STATUS}`, UpdatedArchiveData);
  }
}
