import { Component, EventEmitter, Output, ViewChild } from '@angular/core';
import { SignaturePad } from 'angular2-signaturepad';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Subject } from 'rxjs';

@Component({
  selector: 'sfl-signature',
  templateUrl: './signature.component.html',
  styleUrls: ['./signature.component.scss']
})
export class SignatureComponent {
  public onClose: Subject<any>;
  signatureImg: string;
  @ViewChild('sign') signaturePad: SignaturePad;
  @Output() finalSignature: EventEmitter<string> = new EventEmitter<string>();

  constructor(public _bsModalRef: BsModalRef) {}

  signaturePadOptions: Object = {
    minWidth: 2,
    canvasWidth: 460,
    canvasHeight: 300
  };

  public ngOnInit(): void {
    this.onClose = new Subject();
  }

  ngAfterViewInit() {
    this.signaturePad.set('minWidth', 2);
    this.signaturePad.clear();
  }

  clearSignature() {
    this.signaturePad.clear();
  }

  public onCancel(): void {
    this.onClose.next(false);
    this._bsModalRef.hide();
  }

  saveSignature() {
    const base64Data = this.signaturePad.toDataURL();
    this.signatureImg = base64Data;
    this.finalSignatureInParent();
    this.onClose.next(this.signatureImg);
    this._bsModalRef.hide();
  }

  finalSignatureInParent() {
    return this.finalSignature.emit(this.signatureImg);
  }
}
