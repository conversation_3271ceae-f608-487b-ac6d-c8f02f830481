@import '~@nebular/theme/styles/global/breakpoints';
@import '../../styles/themes';

@include nb-install-component() {
  display: flex;
  justify-content: space-between;
  width: 100%;

  .logo-container {
    display: flex;
    align-items: center;
  }

  nb-action {
    height: auto;
    display: flex;
    align-content: center;
  }

  nb-user {
    cursor: pointer;
  }

  ::ng-deep nb-search button {
    padding: 0 !important;
  }

  .header-container {
    display: flex;
    align-items: center;
    width: auto;

    .sidebar-toggle {
      @include nb-ltr(padding-right, 1.25rem);
      @include nb-rtl(padding-left, 1.25rem);
      text-decoration: none;
      color: nb-theme(text-hint-color);
      nb-icon {
        font-size: 1.75rem;
      }
    }

    .logo {
      padding: 0 1.25rem;
      font-size: 1.75rem;
      @include nb-ltr(border-left, 1px solid nb-theme(divider-color));
      @include nb-rtl(border-right, 1px solid nb-theme(divider-color));
      white-space: nowrap;
      text-decoration: none;
    }
  }

  @include media-breakpoint-down(sm) {
    .control-item {
      display: none;
    }
    .user-action {
      border: none;
      padding: 0;
    }
  }

  @include media-breakpoint-down(is) {
    nb-select {
      display: none;
    }
  }
}

.logo-img {
  width: 180px;
  cursor: pointer;
}
nb-menu.context-menu {
  text-align: left;
}

.nav-container {
  .nav {
    display: flex;
    flex-direction: row;
    list-style-type: none;
    margin-block-end: 0 !important;

    .nav-item {
      list-style-type: none;
      color: white;
      opacity: 0.8;

      a {
        text-decoration: none !important;
      }

      span {
        font-size: 1rem;
      }

      .coming-soon {
        font-size: 9px !important;
        position: absolute;
        margin-top: 20px;
        color: white;
      }
    }

    .active {
      opacity: 1 !important;
      border-bottom: solid 3px white;
    }
  }
}

.logo-container {
  color: #151a30;
  background: url('./../../../../assets/images/qe_logo_white.png') no-repeat;
  background-size: contain;
  height: 45px;
  width: 200px;
}

.userMenu {
  cursor: pointer;
  text-decoration: none !important;
  border-radius: 50%;
  font-weight: bolder;
  text-align: center;
  width: 40px;
  height: 40px;
  padding: 10px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sub-header-container {
  display: flex;
  background-color: white;
  margin: 0px -16px !important;
  .nav-container {
    .nav {
      display: flex;
      flex-direction: row;
      list-style-type: none;
      margin-block-end: 0 !important;
      margin: 0 !important;
      height: 42px;
      justify-content: center;

      .nav-item {
        list-style-type: none;
        color: #464d56;
        opacity: 0.8;

        a {
          padding: 0 !important;
          text-decoration: none !important;
        }

        span {
          font-size: 1rem;
        }

        .coming-soon {
          font-size: 9px !important;
          color: #464d56;
          position: unset !important;
          margin-top: 4px !important;
        }

        .dropdown-toggle::after {
          vertical-align: middle !important;
        }

        .dropdown-menu {
          .dropdown-item {
            padding: 1rem 1.5rem !important;
          }
        }
      }

      .activeSubTab {
        opacity: 1 !important;
        color: #3366ff !important;
      }
    }
  }
}

.c-pointer {
  cursor: pointer;
}

.disabledMenu {
  pointer-events: none !important;
  min-width: 120px;
}

a:hover {
  text-decoration: unset !important;
}

.dropdown-menu {
  .dropdown-item {
    padding: 1rem 1.5rem !important;
    text-decoration: none !important;
  }
}

.zIndex {
  z-index: 99;
}
.env-wrapper {
  position: relative;
  .env-identifier {
    display: flex;
    position: absolute;
    top: -21px;
    letter-spacing: 4px;
    z-index: 9;
    text-transform: uppercase;
    left: 4px;
    color: #000;
    font-weight: bold;
    width: 190px;
    justify-content: center;
  }

  .env-identifier.local {
    background-color: #ff3d71;
  }
  .env-identifier.dev {
    background-color: #fff;
  }
  .env-identifier.stage {
    background-color: #ffc90e;
  }
  .env-identifier.features {
    background-color: #b5e61d;
  }
}
.notification-screen {
  width: 540px;

  @media (max-width: 700px) {
    .modal-dialog-right {
      width: 80%;
    }
  }
  @media only screen and (max-width: 550px) {
    width: 320px;
  }

  .notification-header {
    border-bottom: 1px solid #2c3853;
    .mark-read-link {
      cursor: pointer;
      font-size: 12px;
      .mark-all {
        color: #598bff;
      }
    }
    .small-expand {
      width: auto;
      .expand-icon {
        display: none;
      }
    }
    @media only screen and (max-width: 550px) {
      flex-wrap: wrap;
      .small-expand {
        width: 100%;
        .expand-icon {
          display: block;
        }
      }
      .small-hidden-icon {
        display: none;
      }
    }
  }

  .list-notification {
    max-height: 50vh;
    overflow-y: auto;

    .list-group-item {
      background-color: transparent !important;
      border-bottom: 1px solid #2c3853 !important;

      &:hover {
        background-color: #2c3853 !important;
        border-color: white;
        & p {
          color: #fff;
        }
        & a {
          color: #fff;
        }
        & div {
          color: #fff;
        }
        .dropdown-divider {
          border-color: white !important;
        }
      }
      .description {
        font-size: 14px;
      }
      .user_info {
        border-radius: 50%;
        font-weight: bolder;
        text-align: center;
        width: 40px;
        height: 40px;
        padding: 10px;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #151a30;
      }
      .user_date {
        font-size: 12px;
        font-weight: 300;
      }
      .details {
        .more-details-btn {
          background: #3366ff;
          color: white;
          padding: 0px 5px;
          margin-left: 10px;
        }
      }

      .dropdown-divider {
        border-bottom: 1px solid #2c3853 !important;
        margin: 0px 0px 8px 0px;
      }
    }
  }
}
::ng-deep .pagination-footer .ng-dropdown-panel.ng-select-bottom {
  bottom: 100% !important;
  top: auto !important;
  margin-top: 0 !important;
  margin-bottom: 2px !important;
}

.header-upload-icon {
  .fa.fa-cloud-upload {
    color: #fff;
    font-size: 20px;
  }
}

@keyframes colorShift {
  0% {
    color: red;
  }
  22% {
    color: blue;
  }
  44% {
    color: rgb(234, 114, 33);
  }
  66% {
    color: green;
  }
  100% {
    color: white;
  }
}

.header-upload-icon .fa.fa-cloud-upload {
  animation: colorShift 1.5s infinite; /* Adjust the duration as needed */
  font-size: 20px;
}

.header-site-check-out-btn {
  padding: 2px 10px;
  background-color: #4caf50;
  border-radius: 5px;
}

.header-site-check-in-btn {
  padding: 2px 10px;
  background-color: #ff6e40;
  border-radius: 5px;
}
