<div class="alert-box">
  <div class="modal-header">
    <h4 class="modal-title">File Upload Progress</h4>
    <button type="button" class="close" aria-label="Close" (click)="_bsModalRef.hide()">
      <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
    </button>
  </div>
  <div class="modal-body file-upload-modal">
    <div *ngIf="showProgress; else noFileUploadIsInProgress">
      <div class="mb-2">
        <table class="table table-hover table-bordered">
          <thead>
            <tr>
              <th>#</th>
              <th>File</th>
              <th>Uploading Progress</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let detail of chunkUploadProgressDetails; let i = index">
              <td>{{ i + 1 }}</td>
              <td>{{ detail.fileName }}</td>
              <td>
                <nb-progress-bar
                  [value]="calculateProgress(detail.currentChunk, detail.totalChunks)"
                  status="primary"
                  [displayValue]="true"
                ></nb-progress-bar>
                <!-- <div class="progress-bar">
                  <div class="progress-bar-value"></div>
                </div> -->
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <ng-template #noFileUploadIsInProgress>
      <h6>No file uploads are in progress!</h6>
    </ng-template>
  </div>
  <div class="file-upload-footer" *ngIf="chunkUploadProgressDetails">
    <small>
      1. Please do not refresh or close this browser tab until all files have been uploaded. <br />
      2. Once a file is uploaded, it will be removed from the progress list.<br />
      Note: You can keep using QEST during the file upload process. <br />Once completed, you will receive an email confirming whether the
      upload was successful or failed.
    </small>
    <button nbButton status="basic" size="small" (click)="_bsModalRef.hide()">Okay</button>
  </div>
</div>
