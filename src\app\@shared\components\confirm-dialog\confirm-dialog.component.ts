import { Component, Input, OnInit } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Subject } from 'rxjs';

@Component({
  selector: 'qesolar-confirm-dialog',
  templateUrl: './confirm-dialog.component.html',
  styleUrls: []
})
export class ConfirmDialogComponent implements OnInit {
  public onClose: Subject<boolean>;
  @Input() message = 'Are you sure you want to delete?';
  @Input() isWarning;
  @Input() confirmBtnText = 'Yes';
  @Input() cancelBtnText = 'No';
  @Input() warningMassage = '';
  @Input() isFromWorkOrder = false;
  @Input() showCancelButton = true;
  @Input() showConfirmButton = true;
  @Input() hideCloseIcon = false;

  constructor(public _bsModalRef: BsModalRef) {}

  public ngOnInit(): void {
    this.onClose = new Subject();
  }

  public onConfirm(): void {
    this.onClose.next(true);
    this._bsModalRef.hide();
  }

  public onCancel(): void {
    this.onClose.next(false);
    this._bsModalRef.hide();
  }

  public onCloseBtn(): void {
    this.onClose.next(false);
    this._bsModalRef.hide();
  }
}
