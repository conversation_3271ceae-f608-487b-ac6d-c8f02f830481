<div>
  <div class="col-sm-12 col-xs-12 col-md-12 col-lg-12">
    <qesolar-company-logo></qesolar-company-logo>
  </div>
  <div class="col-sm-12 col-xs-12 col-md-12 col-lg-12">
    <h3 id="title" class="title">Change password</h3>
    <p class="sub-title">Please set a new password</p>
    <form name="resetPassForm" #resetPassForm="ngForm" (ngSubmit)="resetPassForm.valid && resetPassword()" aria-labelledby="title">
      <div class="form-control-group">
        <label class="label" for="input-password">New Password:</label>
        <input
          nbInput
          [(ngModel)]="resetPass.password"
          #password="ngModel"
          type="password"
          id="input-password"
          name="password"
          class="first"
          placeholder="New Password"
          autofocus
          fullWidth
          [status]="password.dirty ? (password.invalid ? 'danger' : 'success') : 'basic'"
          required
          [attr.aria-invalid]="password.invalid && password.touched ? true : null"
        />
        <ng-container *ngIf="password.invalid && password.touched">
          <p class="caption status-danger" *ngIf="password.errors?.required">Password is required!</p>
        </ng-container>
      </div>
      <div class="form-group">
        <label class="label" for="input-re-password">Confirm Password:</label>
        <input
          nbInput
          [(ngModel)]="resetPass.confirmPassword"
          #rePass="ngModel"
          id="input-re-password"
          name="rePass"
          type="password"
          class="last"
          placeholder="Confirm Password"
          fullWidth
          [status]="rePass.touched ? (rePass.invalid || password.value !== rePass.value ? 'danger' : 'success') : 'basic'"
          required
          [attr.aria-invalid]="rePass.invalid && password.value !== rePass.value && rePass.touched ? true : null"
        />
        <ng-container *ngIf="rePass.touched">
          <p class="caption status-danger" *ngIf="rePass.invalid && rePass.errors?.required">Password confirmation is required!</p>
          <p class="caption status-danger" *ngIf="password.value !== rePass.value && !rePass.errors?.required">
            Password does not match the confirm password.
          </p>
        </ng-container>
      </div>
      <button nbButton status="primary" size="medium" type="submit">Submit</button>
    </form>
    <section class="sign-in-or-up" aria-label="Sign in or sign up">
      <p><a class="text-link" routerLink="../login">Back to Log In</a></p>
    </section>
  </div>
</div>
