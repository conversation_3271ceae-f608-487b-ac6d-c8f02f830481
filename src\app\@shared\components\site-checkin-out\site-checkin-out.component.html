<div class="modal-content" id="exclusion" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header">
    <ng-container *ngIf="!updateCheckinDetails && user.checkedInSiteId && !loading; else checkin">
      <h6>Site check-out</h6>
    </ng-container>
    <ng-template #checkin>
      <h6>Site check-in</h6>
    </ng-template>
    <div class="d-flex align-items-center">
      <button type="button" class="close" aria-label="Close" (click)="_bsModalRef.hide()">
        <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
      </button>
    </div>
  </div>

  <!-- Site Check-in -->
  <div class="modal-body" *ngIf="(!user.checkedInSiteId && !loading) || (updateCheckinDetails && user.checkedInSiteId)">
    <form
      name="siteCheckInForm"
      #siteCheckInForm="ngForm"
      aria-labelledby="title"
      autocomplete="off"
      (ngSubmit)="siteCheckInForm.valid && saveSiteCheckIn()"
    >
      <div class="row align-items-center">
        <div class="col-12">
          <button
            *ngIf="user.checkedInSiteId && !loading && updateCheckinDetails"
            class="pull-right"
            nbButton
            status="primary"
            size="small"
            (click)="saveSiteCheckIn(true)"
            type="button"
          >
            Cancel Check-in
          </button>
        </div>
        <div class="col-12">
          <label class="label" for="input-site">
            Site
            <span class="ms-1 text-danger">*</span>
          </label>
          <ng-select
            id="input-site"
            name="siteId"
            #siteId="ngModel"
            [items]="siteList"
            [(ngModel)]="siteCheckinForm.siteId"
            (change)="onSiteUpdate()"
            bindLabel="name"
            bindValue="id"
            notFoundText="No Sites Found"
            placeholder="Select a Site"
            [closeOnSelect]="true"
            required
          >
          </ng-select>
          <sfl-error-msg [control]="siteId" fieldName="Site"></sfl-error-msg>
        </div>
        <div class="col-12 mt-2">
          <label class="label" for="drive-time">
            Drive Time To Site - One Way (Hours)
            <span class="ms-1 text-danger">*</span>
          </label>
          <input
            class="form-control search-textbox sfl-track-input"
            name="driveTime"
            #driveTimeField="ngModel"
            id="drive-time"
            [(ngModel)]="siteCheckinForm.driveTime"
            autocomplete="off"
            required
            sflNumbersOnly
          />
          <sfl-error-msg [control]="driveTimeField" fieldName="Drive Time"></sfl-error-msg>
        </div>
        <div class="col-12 d-flex flex-column mt-2">
          <label class="label" for="input-site">
            Reason For Visit
            <span class="ms-1 text-danger">*</span>
            <div *ngIf="triggerReasonValidation && !siteCheckinForm.reasons.length" class="input-error f-s-13">Reason is required</div>
          </label>
          <ng-container *ngFor="let reason of visitReasons">
            <nb-checkbox
              [value]="reason"
              [checked]="siteCheckinForm.reasons.includes(reason)"
              (change)="onVisitReasonChange($event, reason)"
              [name]="'visit-for-' + reason"
            >
              {{ reason }}
            </nb-checkbox>
          </ng-container>
        </div>
        <div class="col-12" *ngIf="siteCheckinForm.reasons.includes('Other')">
          <label class="label" for="input-site">
            Provide Reason
            <span class="ms-1 text-danger">*</span>
          </label>
          <input
            class="form-control search-textbox sfl-track-input"
            name="reasonOther"
            #reasonOtherField="ngModel"
            id="reasonOther"
            [(ngModel)]="siteCheckinForm.otherReason"
            autocomplete="off"
            [required]="siteCheckinForm.reasons.includes('Other')"
          />
          <sfl-error-msg [control]="reasonOtherField" fieldName="Reason"></sfl-error-msg>
        </div>
        <div class="col-12">
          <label class="label" for="input-site"> Notes </label>
          <input
            class="form-control search-textbox sfl-track-input"
            name="notes"
            #notesField="ngModel"
            id="notes"
            [(ngModel)]="siteCheckinForm.notes"
            autocomplete="off"
          />
        </div>
        <div class="col-12" *ngIf="!updateCheckinDetails && !user.checkedInSiteId">
          <nb-radio-group
            class="d-flex"
            id="safetyOn"
            name="safety-on"
            [(ngModel)]="siteCheckinForm.staySafe"
            #staySafeOn="ngModel"
            required
          >
            <nb-radio value="1" class="inline">Stay Safe is On</nb-radio>
          </nb-radio-group>
          <sfl-error-msg [control]="staySafeOn" fieldName="Stay Safe"></sfl-error-msg>
        </div>
      </div>
    </form>
  </div>
  <!-- Site check in ends -->

  <!-- Site check out -->
  <div class="modal-body" *ngIf="user.checkedInSiteId && !updateCheckinDetails && !loading">
    <div class="pull-right">
      <button nbButton status="basic" size="small">
        <span (click)="updateCheckin()" aria-hidden="true">
          <i class="fa-solid fa-pencil fa-md"></i>
          Update check-in
        </span>
      </button>
    </div>
    <form
      name="siteCheckOutForm"
      #siteCheckOutForm="ngForm"
      aria-labelledby="title"
      autocomplete="off"
      (ngSubmit)="siteCheckOutForm.valid && saveSiteCheckOut()"
    >
      <div class="row align-items-center">
        <div class="col-12 d-flex flex-column">
          <label class="label" for="checkout-checklist">
            Checklist
            <span class="ms-1 text-danger">*</span>
          </label>

          <nb-checkbox
            id="cplwu"
            name="cplwu"
            #cplwu
            [checked]="siteCheckoutChecklist.includes('Contact Portfolio Lead with Update')"
            (change)="onCheckboxChange($event, 'Contact Portfolio Lead with Update')"
            required
            ngModel
          >
            Contact Portfolio Lead with Update
          </nb-checkbox>

          <nb-checkbox
            id="pvm"
            name="pvm"
            #pvm
            [checked]="siteCheckoutChecklist.includes('Production Verified on Monitoring')"
            (change)="onCheckboxChange($event, 'Production Verified on Monitoring')"
            required
            ngModel
          >
            Production Verified on Monitoring
          </nb-checkbox>

          <nb-checkbox
            id="tcu"
            name="tcu"
            #tcu
            [checked]="siteCheckoutChecklist.includes('Tickets Created/Updated')"
            (change)="onCheckboxChange($event, 'Tickets Created/Updated')"
            required
            ngModel
          >
            Tickets Created/Updated
          </nb-checkbox>
          <nb-checkbox
            id="rptu"
            name="rptu"
            #rptu
            [checked]="siteCheckoutChecklist.includes('Report/Photos/Thermals Uploaded')"
            (change)="onCheckboxChange($event, 'Report/Photos/Thermals Uploaded')"
            required
            ngModel
          >
            Report/Photos/Thermals Uploaded
          </nb-checkbox>
          <nb-checkbox
            id="ss"
            name="ss"
            #ss
            [checked]="siteCheckoutChecklist.includes('Site Secured')"
            (change)="onCheckboxChange($event, 'Site Secured')"
            required
            ngModel
          >
            Site Secured
          </nb-checkbox>
        </div>
      </div>
      <div class="ms-2 text-danger" *ngIf="checkoutValidationError">All checklist items are required to be checked.</div>
    </form>
  </div>
  <!-- site check out ends -->
  <div class="modal-footer ModalFooter">
    <button
      *ngIf="!user.checkedInSiteId && !loading && !updateCheckinDetails"
      nbButton
      status="primary"
      size="medium"
      (click)="saveSiteCheckIn()"
      type="submit"
    >
      Check-in
    </button>
    <button
      *ngIf="user.checkedInSiteId && !loading && updateCheckinDetails"
      nbButton
      status="primary"
      size="medium"
      (click)="saveSiteCheckIn()"
      type="submit"
    >
      Update Check-in
    </button>
    <button
      *ngIf="!updateCheckinDetails && user.checkedInSiteId && !loading"
      nbButton
      status="primary"
      size="medium"
      (click)="saveSiteCheckOut()"
      type="submit"
    >
      Check-out
    </button>
    <button nbButton status="basic" size="medium" (click)="onCancel()" type="button">Cancel</button>
  </div>
</div>
