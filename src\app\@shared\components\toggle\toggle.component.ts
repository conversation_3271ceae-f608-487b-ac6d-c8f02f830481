import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { PortfolioService } from '../../../entities/portfolio-management/portfolio.service';
import { SiteService } from '../../../entities/site-management/site.service';
import { UserService } from '../../../entities/user-management/user.service';
import { APP_ROUTES } from '../../constants';
import { Portfolio } from '../../models/portfolio';
import { Site } from '../../models/site.model';
import { User } from '../../models/user.model';
import { AlertService } from '../../services';
import { StorageService } from '../../services/storage.service';

@Component({
  selector: 'qesolar-toggle',
  templateUrl: './toggle.component.html',
  styleUrls: []
})
export class ToggleComponent implements OnInit {
  renderValue: string;
  subscription: Subscription = new Subscription();
  @Input() value;
  @Input() isDisable = false;
  portfolio: Portfolio;
  user: User;
  users: string;
  site: Site;
  constructor(
    private readonly portfolioService: PortfolioService,
    private readonly alertService: AlertService,
    private readonly router: Router,
    private readonly userservice: UserService,
    private readonly siteService: SiteService,
    private readonly storageService: StorageService
  ) {}

  @Input() rowData: any;
  ngOnInit() {
    this.users = this.storageService.get('user').authorities;
  }

  onToggleClick() {
    if (!this.isDisable) {
        this.onInActive();
    }
  }

  onInActive() {
    if (this.users[0] !== 'customer') {
      if (this.router.parseUrl(this.router.url).root.children['primary'].segments[1].path === 'portfolios') {
        this.portfolio = Object.assign({}, this.rowData);
        this.portfolio.isActive = !this.portfolio.isActive;
        this.subscription.add(
          this.portfolioService.activePortfolio(this.portfolio).subscribe({
            next: res => {
              this.router.navigateByUrl(APP_ROUTES.PORTFOLIOS);
              this.rowData.isActive = this.portfolio.isActive;
              this.alertService.showSuccessToast(res.message);
            }
          })
        );
      } else if (this.router.parseUrl(this.router.url).root.children['primary'].segments[2].path === 'users') {
        this.user = Object.assign({}, this.rowData);
        this.user.isActive = !this.user.isActive;
        this.subscription.add(
          this.userservice.activeUser(this.user).subscribe({
            next: res => {
              this.router.navigateByUrl(APP_ROUTES.USER);
              this.rowData.isActive = this.user.isActive;
              this.alertService.showSuccessToast(res.message);
            }
          })
        );
      } else if (this.router.parseUrl(this.router.url).root.children['primary'].segments[1].path === 'sites') {
        this.site = Object.assign({}, this.rowData);
        this.site.isActive = !this.site.isActive;
        this.subscription.add(
          this.siteService.activeSite(this.site).subscribe({
            next: res => {
              this.router.navigateByUrl(APP_ROUTES.SITES);
              this.rowData.isActive = this.site.isActive;
              this.alertService.showSuccessToast(res.message);
            }
          })
        );
      }
    }
  }
}
