import { Directive } from '@angular/core';
import { AbstractControl, NG_VALIDATORS, ValidationErrors, Validator } from '@angular/forms';

@Directive({
  selector: '[sflWhitespaceValidator]',
  providers: [
    {
      provide: NG_VALIDATORS,
      useExisting: WhitespaceValidatorDirective,
      multi: true
    }
  ]
})
export class WhitespaceValidatorDirective implements Validator {
  validate(control: AbstractControl): ValidationErrors | null {
    const value = control.value || '';
    if (value?.length > 0) {
      if (typeof value === 'string' && value.trim().length === 0) {
        return { whitespace: true };
      }
    }
    return null;
  }
}
