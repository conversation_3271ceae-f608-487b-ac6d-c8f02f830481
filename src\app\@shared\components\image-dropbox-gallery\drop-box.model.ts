export class TagListResponseModel {
  public id: number;
  public name: string;
  public isActive: boolean;
  public abbreviation: string;
  public siteNumber: string;
  public customerId: number;
  public portfolioId: number;
  public isAutomationSite: boolean;
  public isArchive: boolean;
}

export class UploadImageReqParams {
  public id: number;
  public siteId: number = null;
  public customerId: number = null;
  public portfolioId: number = null;
  public entityId: number = null;
  public entityNumber: string = '';
  public moduleType: number;
  public files: File;
}

export class CommonGalleryModalConfig {
  public siteId: number = null;
  public customerId: number = null;
  public portfolioId: number = null;
  public entityId: number = null;
  public entityNumber: string = '';
  public fileType: string = 'image';
  public imagePreviewId: number = 0;
  public isCustomerFacing: boolean = false;
  public moduleType: number;
  public parentId: number = null;
  public page: number = 0;
  public sortBy: string = '';
  public direction: string = '';
  public itemsCount: number = 15;
}

export class ImageGalleryListResponse {
  public totalCount: number;
  public fileGallery: ImageList[];
}

export class ImageList {
  public id: number;
  public fileUrl: string;
  public thumbnailUrl: string;
  public customerId: number = null;
  public portfolioId: number = null;
  public siteId: number = null;
  public entityId: number = null;
  public entityNumber: string = '';
  public fileName: string = '';
  public conditionalTag: number[] = [];
  public conditionalTagTxt: string[] = [];
  public deviceTag: number[] = [];
  public deviceTagTxt: string[] = [];
  public fileTag: number[] = [];
  public fileTagTxt: string[] = [];
  public fileType: string = '';
  public notes: string;
  public createdBy: string = '';
  public dateTaken: string = '';
  public createdDate: string = '';
  public isCustomerFacing: boolean;
  public isSelectedForPreview: boolean = false;
  public isPreviewImageEditTag: boolean = false;
}
