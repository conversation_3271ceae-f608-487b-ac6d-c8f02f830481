@import '../../styles/themes';
@import '~@nebular/theme/styles/global/breakpoints';

@include nb-install-component() {
  .menu-sidebar ::ng-deep .scrollable {
    padding-top: nb-theme(layout-padding-top);
  }
}

body {
  overflow: hidden !important;
}

.version_font {
  font-size: 10px !important;
  color: #5e6c84 !important;
}

.layout-container {
  .center {
    min-width: 100% !important;
  }
}

#header {
  background-color: #3366ff;

  qesolar-header {
    display: block;
  }

  nav {
    padding: 0.5rem 1rem 0 1rem !important;
    height: auto !important;
  }
}

#main-container {
  padding: 0.5rem 0.5rem 0px 0.5rem !important;
  margin: 0px 0px 0.5rem 0px !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}
