import { Component, Input, OnInit, Output, OnChanges, EventEmitter, SimpleChanges, AfterViewInit } from '@angular/core';
import { FormArray, FormControl, Validators } from '@angular/forms';
import { OtpValueChangeEvent } from './directives/input-navigations.directive';
import { defaultOptions, SflOtpInputComponentOptions } from './models/otp-input-default.config';

export enum SflOtpStatus {
  SUCCESS = 'success',
  FAILED = 'failed'
}

@Component({
  selector: 'sfl-otp-input',
  templateUrl: './otp-input.component.html',
  styleUrls: ['./otp-input.component.scss']
})
export class OtpInputComponent implements OnInit, OnChanges {
  sflOtpInputArray!: FormArray;
  sflOtpOptions: SflOtpInputComponentOptions = defaultOptions;
  hideInputValues: boolean = defaultOptions.hideInputValues;

  @Input() set options(customOptions: SflOtpInputComponentOptions) {
    this.sflOtpOptions = { ...defaultOptions, ...customOptions };
  }

  @Input() status: SflOtpStatus | null | undefined;
  @Input() disabled = false;
  @Input() otp: string | null | undefined;
  @Output() otpChange = new EventEmitter<string[]>();
  @Output() otpComplete = new EventEmitter<string>();

  // For testing purposes
  get sflOtpOptionsInUse(): SflOtpInputComponentOptions {
    return this.sflOtpOptions;
  }

  get inputType(): string {
    return this.sflOtpOptions.hideInputValues ? 'password' : 'text';
  }

  get isOTPSuccess(): boolean {
    return this.status === SflOtpStatus.SUCCESS;
  }

  get isOTPFailed(): boolean {
    return this.status === SflOtpStatus.FAILED;
  }

  ngOnInit(): void {
    this.initOtpInputArray();
    this.hideInputValues = this.sflOtpOptions?.hideInputValues ?? false;
  }

  ngOnChanges(changes: SimpleChanges): void {
    const otpChange = changes['otp'];
    if (otpChange?.currentValue) {
      if (!otpChange.firstChange) {
        this.setInitialOtp(otpChange.currentValue);
      } else {
        this.sflOtpOptions.autoFocus = false;
      }
    }
  }

  private initOtpInputArray(): void {
    this.sflOtpInputArray = new FormArray(
      Array.from({ length: this.sflOtpOptions.otpLength! }, () => new FormControl('', Validators.required))
    );
    if (this.otp) {
      this.setInitialOtp(this.otp);
    }
  }

  private setInitialOtp(otp: string): void {
    if (this.sflOtpOptions.regexp!.test(otp)) {
      const otpValueArray = otp.split('');
      otpValueArray.forEach((value, index) => {
        this.sflOtpInputArray.controls[index].setValue(value ?? '');
      });
      this.emitOtpValueChange();
      if (otpValueArray.length !== this.sflOtpOptions.otpLength) {
        console.warn('OTP length does not match the provided otpLength option!');
      }
    } else {
      throw new Error('Invalid OTP provided for the component <sfl-otp-input>');
    }
  }

  handleInputChanges($event: OtpValueChangeEvent) {
    const [index, value] = $event;
    this.sflOtpInputArray.controls[index].setValue(value);
    this.emitOtpValueChange();
  }

  handlePasteChange($event: string[]): void {
    if ($event.length === this.sflOtpOptions.otpLength) {
      this.sflOtpInputArray.setValue($event);
    } else {
      $event.map((value, index) => {
        this.sflOtpInputArray.controls[index]?.setValue?.(value);
      });
    }
    this.emitOtpValueChange();
  }

  private emitOtpValueChange(): void {
    this.otpChange.emit(this.sflOtpInputArray.value);
    if (this.sflOtpInputArray.valid) {
      this.otpComplete.emit(this.sflOtpInputArray.value.join(''));
    }
  }

  protected isInputFilled(index: number): boolean {
    return !!this.sflOtpInputArray.controls[index].value;
  }

  reset(): void {
    this.sflOtpInputArray.reset();
  }
}
