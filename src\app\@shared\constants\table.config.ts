export class SflTableConfig {
  public static settings = {
    add: {
      addButtonContent: '<em class="nb-plus"></em>',
      createButtonContent: '<em class="nb-checkmark"></em>',
      cancelButtonContent: '<em class="nb-close"></em>'
    },
    edit: {
      editButtonContent: '<em class="nb-edit"></em>',
      saveButtonContent: '<em class="nb-checkmark"></em>',
      cancelButtonContent: '<em class="nb-close"></em>'
    },
    delete: {
      deleteButtonContent: '<em class="nb-trash"></em>',
      confirmDelete: true
    },
    columns: {}
  };
}
