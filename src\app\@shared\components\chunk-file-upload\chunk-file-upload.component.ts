import { Component, OnInit, ViewChild } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { ChunkUploadProgressDetails } from '../../models/share';
import { AlertService } from '../../services';
import { CommonService } from '../../services/common.service';

@Component({
  selector: 'sfl-chunk-file-upload',
  templateUrl: './chunk-file-upload.component.html',
  styleUrls: ['./chunk-file-upload.component.scss']
})
export class ChunkFileUploadComponent implements OnInit {
  @ViewChild('filesInput') filesInput;
  loading = false;
  subscription: Subscription = new Subscription();
  modalRef: BsModalRef;
  userRole;
  files: File[] = [];
  ticketCreateFileUploadList = [];
  fileTagIds = [];
  notes = '';
  constructor(private readonly commonService: CommonService, private readonly alertService: AlertService) {}

  ngOnInit(): void {}

  async getUploadedFilesInChunk(files: File, maxFileSizeMB: number = 200) {
    const file = files[0];
    // Create an array to store file chunks
    let fileChunks: File[] = [];
    let bufferChunkSize = maxFileSizeMB * (1024 * 1024); // Convert MB to bytes
    let fileStreamPos = 0;
    let endPos = bufferChunkSize;
    let size = file.size;
    let partCount = 0;
    let fileUploadTimeStamp = new Date().getTime();
    let isChunkUpload = false;

    // Split the file into chunks and create File instances
    while (fileStreamPos < size) {
      partCount++;
      const chunk = file.slice(fileStreamPos, endPos);
      const chunkFile = new File([chunk], `${file.name}.part_${partCount}`, {
        type: file.type
      });
      fileChunks.push(chunkFile);

      fileStreamPos = endPos;
      endPos = fileStreamPos + bufferChunkSize;
    }

    // Get total number of chunks
    const totalParts = fileChunks.length;

    // Function to upload chunks recursively
    const uploadChunkRecursively = (index: number, fileUploadTimeStamp: number) => {
      if (index >= totalParts) {
        console.log('File upload complete.');
        this.alertService.showSuccessToast('File upload complete.');
        return;
      }
      const chunk = fileChunks[index];
      let filePartName;
      if (fileChunks.length > 1) {
        isChunkUpload = true;
        filePartName = `${file.name}.part_${index + 1}.${totalParts}`;
      } else {
        filePartName = `${file.name}`;
      }
      const formData = new FormData();
      formData.append('file', chunk, filePartName);
      formData.append('fileName', filePartName);
      formData.append('IsLargeFile', `${isChunkUpload}`);
      formData.append('totalPart', `${totalParts}`);
      formData.append('currentPart', `${index + 1}`);
      formData.append('fileUploadTimeStamp', `${fileUploadTimeStamp}`);
      const chunkDetails: ChunkUploadProgressDetails = {
        fileName: file.name,
        currentChunk: index + 1,
        totalChunks: totalParts,
        fileUploadTimeStamp: fileUploadTimeStamp
      };

      this.commonService.uploadChunk(formData, chunkDetails).subscribe({
        next: () => {
          console.log(`Uploaded chunk ${index + 1} of ${totalParts}`);
          uploadChunkRecursively(index + 1, fileUploadTimeStamp); // Call the next upload
        },
        error: error => {
          console.error(`Error uploading chunk ${index + 1}:`, error);
        }
      });
    };
    uploadChunkRecursively(0, fileUploadTimeStamp);
  }
}
